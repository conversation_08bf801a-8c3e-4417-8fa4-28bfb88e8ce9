#!/usr/bin/env node

// Asset Optimization Script
// Optimizes CSS, JavaScript, and images for production deployment

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class AssetOptimizer {
    constructor() {
        this.publicDir = path.join(__dirname, '..', 'public');
        this.distDir = path.join(__dirname, '..', 'dist');
        this.optimizationStats = {
            cssFiles: 0,
            jsFiles: 0,
            imageFiles: 0,
            originalSize: 0,
            optimizedSize: 0,
            compressionRatio: 0
        };
    }

    // Main optimization process
    async optimize() {
        console.log('🚀 Starting asset optimization...');
        
        try {
            // Create dist directory
            this.ensureDistDirectory();
            
            // Optimize CSS files
            await this.optimizeCSS();
            
            // Optimize JavaScript files
            await this.optimizeJavaScript();
            
            // Optimize images
            await this.optimizeImages();
            
            // Generate asset manifest
            await this.generateAssetManifest();
            
            // Generate service worker for caching
            await this.generateServiceWorker();
            
            // Calculate compression ratio
            this.calculateCompressionRatio();
            
            // Display optimization results
            this.displayResults();
            
            console.log('✅ Asset optimization completed successfully!');
            
        } catch (error) {
            console.error('❌ Asset optimization failed:', error);
            process.exit(1);
        }
    }

    // Ensure dist directory exists
    ensureDistDirectory() {
        if (!fs.existsSync(this.distDir)) {
            fs.mkdirSync(this.distDir, { recursive: true });
        }
        
        // Create subdirectories
        const subdirs = ['css', 'js', 'images'];
        subdirs.forEach(dir => {
            const dirPath = path.join(this.distDir, dir);
            if (!fs.existsSync(dirPath)) {
                fs.mkdirSync(dirPath, { recursive: true });
            }
        });
    }

    // Optimize CSS files
    async optimizeCSS() {
        console.log('📝 Optimizing CSS files...');
        
        const cssDir = path.join(this.publicDir, 'css');
        const cssFiles = fs.readdirSync(cssDir).filter(file => file.endsWith('.css'));
        
        for (const file of cssFiles) {
            const inputPath = path.join(cssDir, file);
            const outputPath = path.join(this.distDir, 'css', file);
            
            try {
                // Read CSS content
                let cssContent = fs.readFileSync(inputPath, 'utf8');
                const originalSize = Buffer.byteLength(cssContent, 'utf8');
                
                // Minify CSS
                cssContent = this.minifyCSS(cssContent);
                
                // Add cache busting hash
                const hash = this.generateHash(cssContent);
                const hashedFilename = file.replace('.css', `.${hash}.css`);
                const hashedOutputPath = path.join(this.distDir, 'css', hashedFilename);
                
                // Write optimized CSS
                fs.writeFileSync(hashedOutputPath, cssContent);
                
                const optimizedSize = Buffer.byteLength(cssContent, 'utf8');
                this.optimizationStats.cssFiles++;
                this.optimizationStats.originalSize += originalSize;
                this.optimizationStats.optimizedSize += optimizedSize;
                
                console.log(`  ✅ ${file} → ${hashedFilename} (${this.formatBytes(originalSize)} → ${this.formatBytes(optimizedSize)})`);
                
            } catch (error) {
                console.error(`  ❌ Failed to optimize ${file}:`, error.message);
            }
        }
    }

    // Optimize JavaScript files
    async optimizeJavaScript() {
        console.log('📜 Optimizing JavaScript files...');
        
        const jsDir = path.join(this.publicDir, 'js');
        const jsFiles = fs.readdirSync(jsDir).filter(file => file.endsWith('.js'));
        
        for (const file of jsFiles) {
            const inputPath = path.join(jsDir, file);
            const outputPath = path.join(this.distDir, 'js', file);
            
            try {
                // Read JS content
                let jsContent = fs.readFileSync(inputPath, 'utf8');
                const originalSize = Buffer.byteLength(jsContent, 'utf8');
                
                // Minify JavaScript
                jsContent = this.minifyJavaScript(jsContent);
                
                // Add cache busting hash
                const hash = this.generateHash(jsContent);
                const hashedFilename = file.replace('.js', `.${hash}.js`);
                const hashedOutputPath = path.join(this.distDir, 'js', hashedFilename);
                
                // Write optimized JS
                fs.writeFileSync(hashedOutputPath, jsContent);
                
                const optimizedSize = Buffer.byteLength(jsContent, 'utf8');
                this.optimizationStats.jsFiles++;
                this.optimizationStats.originalSize += originalSize;
                this.optimizationStats.optimizedSize += optimizedSize;
                
                console.log(`  ✅ ${file} → ${hashedFilename} (${this.formatBytes(originalSize)} → ${this.formatBytes(optimizedSize)})`);
                
            } catch (error) {
                console.error(`  ❌ Failed to optimize ${file}:`, error.message);
            }
        }
    }

    // Optimize images
    async optimizeImages() {
        console.log('🖼️ Optimizing images...');
        
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'];
        const findImages = (dir) => {
            let images = [];
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const itemPath = path.join(dir, item);
                const stat = fs.statSync(itemPath);
                
                if (stat.isDirectory()) {
                    images = images.concat(findImages(itemPath));
                } else if (imageExtensions.some(ext => item.toLowerCase().endsWith(ext))) {
                    images.push(itemPath);
                }
            }
            
            return images;
        };
        
        const images = findImages(this.publicDir);
        
        for (const imagePath of images) {
            try {
                const relativePath = path.relative(this.publicDir, imagePath);
                const outputPath = path.join(this.distDir, 'images', path.basename(imagePath));
                
                // Copy and optimize image
                const originalSize = fs.statSync(imagePath).size;
                
                // For now, just copy the image (in production, you'd use image optimization libraries)
                fs.copyFileSync(imagePath, outputPath);
                
                const optimizedSize = fs.statSync(outputPath).size;
                this.optimizationStats.imageFiles++;
                this.optimizationStats.originalSize += originalSize;
                this.optimizationStats.optimizedSize += optimizedSize;
                
                console.log(`  ✅ ${relativePath} (${this.formatBytes(originalSize)})`);
                
            } catch (error) {
                console.error(`  ❌ Failed to optimize ${imagePath}:`, error.message);
            }
        }
    }

    // Generate asset manifest
    async generateAssetManifest() {
        console.log('📋 Generating asset manifest...');
        
        const manifest = {
            version: Date.now(),
            assets: {},
            timestamp: new Date().toISOString()
        };
        
        // Scan dist directory for assets
        const scanDirectory = (dir, prefix = '') => {
            const items = fs.readdirSync(dir);
            
            for (const item of items) {
                const itemPath = path.join(dir, item);
                const stat = fs.statSync(itemPath);
                
                if (stat.isDirectory()) {
                    scanDirectory(itemPath, `${prefix}${item}/`);
                } else {
                    const relativePath = `${prefix}${item}`;
                    manifest.assets[relativePath] = {
                        size: stat.size,
                        hash: this.generateHash(fs.readFileSync(itemPath)),
                        lastModified: stat.mtime.toISOString()
                    };
                }
            }
        };
        
        scanDirectory(this.distDir);
        
        // Write manifest
        const manifestPath = path.join(this.distDir, 'manifest.json');
        fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
        
        console.log(`  ✅ Asset manifest generated with ${Object.keys(manifest.assets).length} assets`);
    }

    // Generate service worker for caching
    async generateServiceWorker() {
        console.log('⚙️ Generating service worker...');
        
        const swContent = `
// Naroop Service Worker - Generated automatically
const CACHE_NAME = 'naroop-v${Date.now()}';
const STATIC_CACHE = 'naroop-static-v${Date.now()}';

// Assets to cache
const CACHE_ASSETS = [
    '/',
    '/index.html',
    '/public/css/performance.css',
    '/public/css/main.css',
    '/public/css/responsive.css',
    '/public/js/performance-optimizer.js',
    '/public/js/core.js',
    '/public/js/error-handler.js'
];

// Install event
self.addEventListener('install', event => {
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => cache.addAll(CACHE_ASSETS))
            .then(() => self.skipWaiting())
    );
});

// Activate event
self.addEventListener('activate', event => {
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== CACHE_NAME && cacheName !== STATIC_CACHE) {
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => self.clients.claim())
    );
});

// Fetch event
self.addEventListener('fetch', event => {
    event.respondWith(
        caches.match(event.request)
            .then(response => {
                if (response) {
                    return response;
                }
                return fetch(event.request);
            })
    );
});
`;
        
        const swPath = path.join(this.distDir, 'sw.js');
        fs.writeFileSync(swPath, swContent.trim());
        
        console.log('  ✅ Service worker generated');
    }

    // Minify CSS (basic implementation)
    minifyCSS(css) {
        return css
            .replace(/\/\*[\s\S]*?\*\//g, '') // Remove comments
            .replace(/\s+/g, ' ') // Collapse whitespace
            .replace(/;\s*}/g, '}') // Remove unnecessary semicolons
            .replace(/\s*{\s*/g, '{') // Remove spaces around braces
            .replace(/}\s*/g, '}') // Remove spaces after braces
            .replace(/:\s*/g, ':') // Remove spaces after colons
            .replace(/;\s*/g, ';') // Remove spaces after semicolons
            .trim();
    }

    // Minify JavaScript (basic implementation)
    minifyJavaScript(js) {
        return js
            .replace(/\/\*[\s\S]*?\*\//g, '') // Remove block comments
            .replace(/\/\/.*$/gm, '') // Remove line comments
            .replace(/\s+/g, ' ') // Collapse whitespace
            .replace(/;\s*}/g, '}') // Clean up semicolons
            .trim();
    }

    // Generate hash for cache busting
    generateHash(content) {
        const crypto = require('crypto');
        return crypto.createHash('md5').update(content).digest('hex').substring(0, 8);
    }

    // Format bytes for display
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Calculate compression ratio
    calculateCompressionRatio() {
        if (this.optimizationStats.originalSize > 0) {
            this.optimizationStats.compressionRatio = 
                ((this.optimizationStats.originalSize - this.optimizationStats.optimizedSize) / 
                 this.optimizationStats.originalSize * 100).toFixed(2);
        }
    }

    // Display optimization results
    displayResults() {
        console.log('\n📊 Optimization Results:');
        console.log('========================');
        console.log(`CSS Files: ${this.optimizationStats.cssFiles}`);
        console.log(`JS Files: ${this.optimizationStats.jsFiles}`);
        console.log(`Image Files: ${this.optimizationStats.imageFiles}`);
        console.log(`Original Size: ${this.formatBytes(this.optimizationStats.originalSize)}`);
        console.log(`Optimized Size: ${this.formatBytes(this.optimizationStats.optimizedSize)}`);
        console.log(`Compression Ratio: ${this.optimizationStats.compressionRatio}%`);
        console.log(`Space Saved: ${this.formatBytes(this.optimizationStats.originalSize - this.optimizationStats.optimizedSize)}`);
    }
}

// Run optimization if called directly
if (require.main === module) {
    const optimizer = new AssetOptimizer();
    optimizer.optimize();
}

module.exports = AssetOptimizer;
