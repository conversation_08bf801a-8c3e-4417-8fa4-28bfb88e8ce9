/* Naroop Main App (index.html) Specific Styles */

/* Main app layout */
.app-container {
    display: grid;
    grid-template-rows: var(--header-height) 1fr;
    min-height: 100vh;
}

.app-main {
    display: grid;
    grid-template-columns: var(--sidebar-width) 1fr 300px;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg);
    max-width: var(--container-max-width);
    margin: 0 auto;
}

/* Sidebar styles */
.sidebar {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    height: fit-content;
    position: sticky;
    top: calc(var(--header-height) + var(--spacing-lg));
}

.nav-menu {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    margin-bottom: var(--spacing-xs);
    text-decoration: none;
    color: var(--text-secondary);
}

.nav-item:hover {
    background: var(--surface-color);
    color: var(--text-primary);
}

.nav-item.active {
    background: var(--primary-color);
    color: white;
}

.nav-item-icon {
    font-size: var(--font-size-lg);
    width: 24px;
    text-align: center;
}

.nav-item-text {
    font-weight: var(--font-weight-medium);
}

/* Feed section */
.feed-section {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
}

.feed-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-lg);
    padding-bottom: var(--spacing-md);
    border-bottom: 1px solid var(--divider-color);
}

.feed-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    margin: 0;
}

.create-post-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    cursor: pointer;
    font-weight: var(--font-weight-medium);
    transition: all var(--transition-fast);
}

.create-post-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

/* Trending section */
.trending-section {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    height: fit-content;
    position: sticky;
    top: calc(var(--header-height) + var(--spacing-lg));
}

.trending-header {
    margin-bottom: var(--spacing-md);
    padding-bottom: var(--spacing-sm);
    border-bottom: 1px solid var(--divider-color);
}

.trending-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    margin: 0;
}

.trending-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--divider-color);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.trending-item:last-child {
    border-bottom: none;
}

.trending-item:hover {
    background: var(--surface-color);
    margin: 0 calc(-1 * var(--spacing-sm));
    padding-left: var(--spacing-sm);
    padding-right: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.trending-rank {
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    min-width: 20px;
}

.trending-topic {
    flex: 1;
    font-weight: var(--font-weight-medium);
}

.trending-count {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
}

/* Empty states */
.empty-state {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--text-muted);
}

.empty-state-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
}

.empty-state h3 {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

.empty-state p {
    margin-bottom: var(--spacing-lg);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Error states */
.error-state {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--error-color);
}

.error-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
}

/* Responsive design for main app */
@media (max-width: 1024px) {
    .app-main {
        grid-template-columns: 1fr 280px;
        gap: var(--spacing-md);
    }

    .sidebar {
        display: none;
    }
}

@media (max-width: 768px) {
    .app-main {
        grid-template-columns: 1fr;
        gap: var(--spacing-sm);
        padding: var(--spacing-md);
    }

    .trending-section {
        display: none;
    }

    .feed-header {
        flex-direction: column;
        gap: var(--spacing-md);
        align-items: stretch;
    }

    .create-post-btn {
        justify-content: center;
    }
}