/* Naroop Mobile Performance Optimization */

/* Touch optimization */
.touch-optimized {
    touch-action: manipulation;
    -webkit-tap-highlight-color: transparent;
}

.touch-target {
    min-height: 44px;
    min-width: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Scroll performance */
.smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

.momentum-scroll {
    overflow-y: scroll;
    -webkit-overflow-scrolling: touch;
}

/* Reduce repaints on mobile */
.mobile-optimized {
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Battery-friendly animations */
@media (max-width: 768px) {
    .battery-friendly {
        animation-duration: 0.2s !important;
        transition-duration: 0.2s !important;
    }

    .reduce-motion {
        animation: none !important;
        transition: none !important;
    }
}

/* Network-aware loading */
.slow-network .heavy-content {
    display: none;
}

.slow-network .light-content {
    display: block;
}

/* Mobile-specific layout optimizations */
@media (max-width: 768px) {
    .mobile-stack {
        flex-direction: column !important;
    }

    .mobile-hide {
        display: none !important;
    }

    .mobile-full-width {
        width: 100% !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    .mobile-center {
        text-align: center !important;
    }

    .mobile-padding {
        padding: var(--spacing-sm) !important;
    }

    .mobile-margin {
        margin: var(--spacing-sm) !important;
    }
}

/* Viewport optimization */
@media (max-width: 480px) {
    .viewport-optimized {
        max-width: 100vw;
        overflow-x: hidden;
    }
}

/* Memory management for mobile */
.mobile-memory-efficient {
    contain: layout style paint;
    content-visibility: auto;
    contain-intrinsic-size: 100px;
}

/* Reduce JavaScript execution on mobile */
.mobile-js-light {
    pointer-events: none;
}

.mobile-js-light.interactive {
    pointer-events: auto;
}

/* Mobile-specific font optimization */
@media (max-width: 768px) {
    .mobile-font-size {
        font-size: 16px !important; /* Prevent zoom on iOS */
    }

    .mobile-line-height {
        line-height: 1.4 !important;
    }
}

/* Gesture optimization */
.swipe-enabled {
    touch-action: pan-x pan-y;
}

.pinch-zoom-disabled {
    touch-action: manipulation;
}

/* Mobile loading states */
.mobile-loading {
    background: var(--surface-color);
    border-radius: var(--border-radius-sm);
    height: 60px;
    position: relative;
    overflow: hidden;
}

.mobile-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    animation: mobile-shimmer 1s infinite;
}

@keyframes mobile-shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Reduce complexity on mobile */
@media (max-width: 768px) {
    .mobile-simple {
        box-shadow: none !important;
        border-radius: var(--border-radius-sm) !important;
        background: var(--card-background) !important;
    }

    .mobile-flat {
        background: transparent !important;
        border: 1px solid var(--border-color) !important;
        box-shadow: none !important;
    }
}

/* Mobile-specific performance hints */
.mobile-critical {
    /* Critical content for mobile */
    priority: high;
}

.mobile-deferred {
    /* Deferred content for mobile */
    priority: low;
    loading: lazy;
}

/* Connection-aware optimizations */
@media (max-width: 768px) {
    .slow-connection .high-quality {
        display: none;
    }

    .slow-connection .low-quality {
        display: block;
    }

    .fast-connection .high-quality {
        display: block;
    }

    .fast-connection .low-quality {
        display: none;
    }
}