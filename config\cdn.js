// CDN Configuration for Naroop Platform
// Handles asset delivery optimization and CDN integration

class CDNManager {
    constructor() {
        this.config = {
            // CDN providers configuration
            providers: {
                cloudflare: {
                    enabled: process.env.CLOUDFLARE_ENABLED === 'true',
                    zone: process.env.CLOUDFLARE_ZONE,
                    apiToken: process.env.CLOUDFLARE_API_TOKEN,
                    baseUrl: process.env.CLOUDFLARE_CDN_URL || 'https://cdn.naroop.com'
                },
                aws: {
                    enabled: process.env.AWS_CDN_ENABLED === 'true',
                    distributionId: process.env.AWS_CLOUDFRONT_DISTRIBUTION_ID,
                    baseUrl: process.env.AWS_CDN_URL || 'https://d1234567890.cloudfront.net'
                },
                generic: {
                    enabled: process.env.GENERIC_CDN_ENABLED === 'true',
                    baseUrl: process.env.CDN_BASE_URL || ''
                }
            },
            
            // Asset types and their cache settings
            assetTypes: {
                css: {
                    maxAge: 31536000, // 1 year
                    extensions: ['.css'],
                    compress: true,
                    minify: true
                },
                js: {
                    maxAge: 31536000, // 1 year
                    extensions: ['.js'],
                    compress: true,
                    minify: true
                },
                images: {
                    maxAge: 31536000, // 1 year
                    extensions: ['.jpg', '.jpeg', '.png', '.gif', '.svg', '.webp'],
                    compress: true,
                    optimize: true
                },
                fonts: {
                    maxAge: 31536000, // 1 year
                    extensions: ['.woff', '.woff2', '.ttf', '.eot'],
                    compress: true
                },
                html: {
                    maxAge: 3600, // 1 hour
                    extensions: ['.html'],
                    compress: true
                }
            },
            
            // Performance optimization settings
            optimization: {
                enableBrotli: true,
                enableGzip: true,
                enableWebP: true,
                enableHTTP2Push: true,
                enablePreload: true
            }
        };
    }

    // Get CDN URL for an asset
    getCDNUrl(assetPath) {
        const activeProvider = this.getActiveProvider();
        
        if (!activeProvider || !activeProvider.baseUrl) {
            return assetPath; // Return original path if no CDN configured
        }
        
        // Remove leading slash if present
        const cleanPath = assetPath.startsWith('/') ? assetPath.substring(1) : assetPath;
        
        return `${activeProvider.baseUrl}/${cleanPath}`;
    }

    // Get active CDN provider
    getActiveProvider() {
        for (const [name, provider] of Object.entries(this.config.providers)) {
            if (provider.enabled && provider.baseUrl) {
                return provider;
            }
        }
        return null;
    }

    // Generate cache headers for asset type
    getCacheHeaders(filePath) {
        const extension = this.getFileExtension(filePath);
        const assetType = this.getAssetType(extension);
        
        if (!assetType) {
            return {
                'Cache-Control': 'public, max-age=3600', // Default 1 hour
                'Vary': 'Accept-Encoding'
            };
        }
        
        const headers = {
            'Cache-Control': `public, max-age=${assetType.maxAge}, immutable`,
            'Vary': 'Accept-Encoding'
        };
        
        // Add compression headers
        if (assetType.compress) {
            headers['Content-Encoding'] = 'gzip';
        }
        
        // Add security headers for certain asset types
        if (extension === '.js') {
            headers['X-Content-Type-Options'] = 'nosniff';
        }
        
        return headers;
    }

    // Get asset type configuration
    getAssetType(extension) {
        for (const [type, config] of Object.entries(this.config.assetTypes)) {
            if (config.extensions.includes(extension.toLowerCase())) {
                return config;
            }
        }
        return null;
    }

    // Get file extension
    getFileExtension(filePath) {
        const lastDot = filePath.lastIndexOf('.');
        return lastDot !== -1 ? filePath.substring(lastDot) : '';
    }

    // Generate preload headers for critical resources
    generatePreloadHeaders() {
        const preloadResources = [
            { href: '/public/css/performance.css', as: 'style' },
            { href: '/public/css/main.css', as: 'style' },
            { href: '/public/js/performance-optimizer.js', as: 'script' },
            { href: '/public/js/core.js', as: 'script' }
        ];
        
        return preloadResources.map(resource => {
            const cdnUrl = this.getCDNUrl(resource.href);
            return `<${cdnUrl}>; rel=preload; as=${resource.as}`;
        }).join(', ');
    }

    // Generate resource hints for DNS prefetch and preconnect
    generateResourceHints() {
        const hints = [];
        const activeProvider = this.getActiveProvider();
        
        if (activeProvider && activeProvider.baseUrl) {
            const cdnDomain = new URL(activeProvider.baseUrl).hostname;
            hints.push(`<https://${cdnDomain}>; rel=preconnect`);
            hints.push(`<https://${cdnDomain}>; rel=dns-prefetch`);
        }
        
        // Add other external domains
        const externalDomains = [
            'fonts.googleapis.com',
            'fonts.gstatic.com',
            'www.gstatic.com'
        ];
        
        externalDomains.forEach(domain => {
            hints.push(`<https://${domain}>; rel=preconnect`);
            hints.push(`<https://${domain}>; rel=dns-prefetch`);
        });
        
        return hints.join(', ');
    }

    // Middleware for serving optimized assets
    assetMiddleware() {
        return (req, res, next) => {
            const filePath = req.path;
            
            // Check if this is a static asset request
            if (this.isStaticAsset(filePath)) {
                // Set cache headers
                const cacheHeaders = this.getCacheHeaders(filePath);
                Object.entries(cacheHeaders).forEach(([key, value]) => {
                    res.setHeader(key, value);
                });
                
                // Add CDN headers
                res.setHeader('X-CDN-Cache', 'MISS');
                res.setHeader('X-Served-By', 'Naroop-Origin');
                
                // Add CORS headers for cross-origin requests
                res.setHeader('Access-Control-Allow-Origin', '*');
                res.setHeader('Access-Control-Allow-Methods', 'GET, HEAD, OPTIONS');
                res.setHeader('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');
                
                // Handle OPTIONS requests
                if (req.method === 'OPTIONS') {
                    return res.status(200).end();
                }
            }
            
            next();
        };
    }

    // Check if path is a static asset
    isStaticAsset(filePath) {
        const staticPaths = ['/public/', '/dist/', '/assets/'];
        return staticPaths.some(path => filePath.startsWith(path));
    }

    // Generate optimized HTML with CDN URLs
    optimizeHTML(html) {
        let optimizedHTML = html;
        
        // Replace asset URLs with CDN URLs
        const assetRegex = /(href|src)=["']([^"']+\.(css|js|png|jpg|jpeg|gif|svg|webp|woff|woff2|ttf|eot))["']/gi;
        
        optimizedHTML = optimizedHTML.replace(assetRegex, (match, attr, url, ext) => {
            const cdnUrl = this.getCDNUrl(url);
            return `${attr}="${cdnUrl}"`;
        });
        
        // Add preload headers to HTML head
        const preloadHeaders = this.generatePreloadHeaders();
        if (preloadHeaders) {
            const linkTags = preloadHeaders.split(', ').map(header => {
                const match = header.match(/<([^>]+)>; rel=([^;]+); as=([^;]+)/);
                if (match) {
                    return `<link rel="${match[2]}" href="${match[1]}" as="${match[3]}">`;
                }
                return '';
            }).filter(Boolean).join('\n    ');
            
            optimizedHTML = optimizedHTML.replace(
                '</head>',
                `    ${linkTags}\n</head>`
            );
        }
        
        // Add resource hints
        const resourceHints = this.generateResourceHints();
        if (resourceHints) {
            const hintTags = resourceHints.split(', ').map(hint => {
                const match = hint.match(/<([^>]+)>; rel=([^;]+)/);
                if (match) {
                    return `<link rel="${match[2]}" href="${match[1]}">`;
                }
                return '';
            }).filter(Boolean).join('\n    ');
            
            optimizedHTML = optimizedHTML.replace(
                '</head>',
                `    ${hintTags}\n</head>`
            );
        }
        
        return optimizedHTML;
    }

    // Purge CDN cache
    async purgeCDNCache(urls = []) {
        const activeProvider = this.getActiveProvider();
        
        if (!activeProvider) {
            console.log('No CDN provider configured for cache purging');
            return;
        }
        
        try {
            if (activeProvider === this.config.providers.cloudflare && activeProvider.apiToken) {
                await this.purgeCloudflareCache(urls);
            } else if (activeProvider === this.config.providers.aws && activeProvider.distributionId) {
                await this.purgeAWSCache(urls);
            } else {
                console.log('CDN cache purging not implemented for current provider');
            }
        } catch (error) {
            console.error('Error purging CDN cache:', error);
        }
    }

    // Purge Cloudflare cache
    async purgeCloudflareCache(urls) {
        // Implementation would go here for Cloudflare API
        console.log('Cloudflare cache purge requested for:', urls);
    }

    // Purge AWS CloudFront cache
    async purgeAWSCache(urls) {
        // Implementation would go here for AWS CloudFront API
        console.log('AWS CloudFront cache purge requested for:', urls);
    }

    // Get CDN configuration
    getConfig() {
        return this.config;
    }

    // Update CDN configuration
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
    }
}

module.exports = CDNManager;
