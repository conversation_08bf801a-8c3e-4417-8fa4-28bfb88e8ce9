# Naroop JavaScript Architecture

This directory contains all the JavaScript modules for the Naroop project, organized by functionality and following modern ES6+ patterns.

## Core Modules

### `core.js`
- Main application initialization and coordination
- Event bus system for inter-module communication
- Global state management
- Page-specific initialization
- **Dependencies**: `authentication.js`

### `firebase-config.js`
- Firebase initialization and configuration
- Authentication state management
- Demo mode support for development
- Server communication for auth tokens
- **Dependencies**: None (standalone)

## Authentication & User Management

### `authentication.js`
- User authentication flows (sign in, sign up, sign out)
- Authentication state listeners
- User session management
- Integration with Firebase Auth
- **Dependencies**: `firebase-config.js`

### `profile.js`
- User profile management and display
- Profile editing functionality
- User statistics and information
- Profile modal system
- **Dependencies**: `core.js`, `authentication.js`

## Navigation & UI

### `navigation.js`
- Single-page application routing
- Navigation state management
- Section switching and history management
- Dynamic content loading
- **Dependencies**: `core.js`

### `modal-system.js`
- Modal creation and management
- Focus trapping and accessibility
- Modal stacking and backdrop handling
- Event delegation for modal triggers
- **Dependencies**: None (standalone)

## Content Management

### `posts.js`
- Post creation, display, and interaction
- Social media functionality (likes, comments, shares)
- Post form handling and validation
- Empty states and loading management
- **Dependencies**: `core.js`, `authentication.js`

## Accessibility & Preferences

### `accessibility-preferences.js`
- User accessibility settings
- Theme switching (light, dark, high-contrast)
- Font size adjustment
- Reduced motion preferences
- Keyboard navigation support
- **Dependencies**: None (standalone)

## Specialized Features

### `scroll-sections.js`
- Smooth scrolling between sections
- Scroll-snap functionality
- Touch and keyboard navigation
- Section indicators and navigation
- **Dependencies**: None (standalone)

## Module Architecture

### ES6 Modules
All modules use ES6 import/export syntax:
```javascript
import { naroopCore } from './core.js';
export { ModuleName, moduleInstance };
export default moduleInstance;
```

### Event System
Modules communicate through the core event bus:
```javascript
// Emit events
naroopCore.emit('eventName', { data });

// Listen for events
naroopCore.on('eventName', (event) => {
  // Handle event
});
```

### Module Registration
Modules register themselves with the core:
```javascript
naroopCore.registerModule('moduleName', moduleInstance);
```

## Loading Order

1. **Core Infrastructure**
   - `firebase-config.js` (Firebase initialization)
   - `modal-system.js` (UI foundation)
   - `accessibility-preferences.js` (User preferences)

2. **Authentication Layer**
   - `authentication.js` (User management)

3. **Application Core**
   - `core.js` (Main application)

4. **Feature Modules**
   - `navigation.js` (Routing)
   - `posts.js` (Content)
   - `profile.js` (User profiles)

5. **Specialized Features**
   - `scroll-sections.js` (Page-specific)

## Error Handling

All modules implement consistent error handling:
- Try-catch blocks for async operations
- Graceful degradation for missing dependencies
- User-friendly error messages
- Console logging for debugging

## Performance Considerations

- **Lazy Loading**: Modules load only when needed
- **Event Delegation**: Efficient event handling
- **Memory Management**: Proper cleanup and unsubscription
- **Async Operations**: Non-blocking initialization

## Browser Support

- ES6+ features (modules, async/await, classes)
- Modern browsers (Chrome 90+, Firefox 88+, Safari 14+, Edge 90+)
- Progressive enhancement for older browsers
- Polyfills not included (use build tools if needed)

## Development Guidelines

1. **Module Pattern**: Each file exports a class and instance
2. **Dependency Injection**: Pass dependencies through imports
3. **Event-Driven**: Use the event bus for communication
4. **Error Handling**: Always handle async errors gracefully
5. **Accessibility**: Include ARIA attributes and keyboard support
6. **Performance**: Optimize for mobile and slow networks

## Testing

Each module should be testable in isolation:
- Mock dependencies for unit tests
- Use the event system for integration tests
- Test error conditions and edge cases
- Verify accessibility features
