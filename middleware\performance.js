// Backend Performance Optimization Middleware
// Handles server-side performance optimizations for the Naroop platform

const compression = require('compression');
const helmet = require('helmet');

class BackendPerformanceOptimizer {
    constructor() {
        this.cache = new Map();
        this.requestMetrics = {
            totalRequests: 0,
            averageResponseTime: 0,
            slowRequests: 0,
            errorRequests: 0
        };
        
        this.cacheConfig = {
            maxSize: 1000,
            ttl: 300000, // 5 minutes
            cleanupInterval: 60000 // 1 minute
        };
        
        this.performanceThresholds = {
            slowRequestTime: 1000, // 1 second
            maxMemoryUsage: 512 * 1024 * 1024, // 512MB
            maxCacheSize: 100 * 1024 * 1024 // 100MB
        };
        
        this.startCleanupInterval();
    }

    // Initialize performance middleware
    initializeMiddleware(app) {
        console.log('🚀 Initializing backend performance optimizations...');
        
        // Compression middleware
        app.use(compression({
            level: 6,
            threshold: 1024,
            filter: (req, res) => {
                if (req.headers['x-no-compression']) {
                    return false;
                }
                return compression.filter(req, res);
            }
        }));
        
        // Security headers with performance considerations
        app.use(helmet({
            contentSecurityPolicy: {
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
                    fontSrc: ["'self'", "https://fonts.gstatic.com"],
                    scriptSrc: ["'self'", "https://www.gstatic.com"],
                    imgSrc: ["'self'", "data:", "https:"],
                    connectSrc: ["'self'", "https://www.gstatic.com"]
                }
            },
            crossOriginEmbedderPolicy: false // Better performance
        }));
        
        // Performance monitoring middleware
        app.use(this.performanceMonitoringMiddleware.bind(this));
        
        // Response caching middleware
        app.use(this.responseCachingMiddleware.bind(this));
        
        // Request optimization middleware
        app.use(this.requestOptimizationMiddleware.bind(this));
        
        console.log('✅ Backend performance optimizations initialized');
    }

    // Performance monitoring middleware
    performanceMonitoringMiddleware(req, res, next) {
        const startTime = process.hrtime.bigint();
        
        // Track request
        this.requestMetrics.totalRequests++;
        
        // Override res.end to capture response time
        const originalEnd = res.end;
        res.end = function(...args) {
            const endTime = process.hrtime.bigint();
            const responseTime = Number(endTime - startTime) / 1000000; // Convert to milliseconds
            
            // Update metrics
            this.updateResponseTimeMetrics(responseTime);
            
            // Log slow requests
            if (responseTime > this.performanceThresholds.slowRequestTime) {
                console.warn(`⚠️ Slow request: ${req.method} ${req.path} (${responseTime.toFixed(2)}ms)`);
                this.requestMetrics.slowRequests++;
            }
            
            // Add performance headers - only if headers haven't been sent yet
            if (!res.headersSent) {
                try {
                    res.setHeader('X-Response-Time', `${responseTime.toFixed(2)}ms`);
                    res.setHeader('X-Request-ID', req.id || 'unknown');
                } catch (error) {
                    console.warn('Could not set performance headers:', error.message);
                }
            }
            
            return originalEnd.apply(res, args);
        }.bind(this); // Bind this to the function to access class methods
        
        next();
    }

    // Response caching middleware
    responseCachingMiddleware(req, res, next) {
        // Only cache GET requests
        if (req.method !== 'GET') {
            return next();
        }
        
        // Skip caching for authenticated requests
        if (req.headers.authorization || req.headers.cookie) {
            return next();
        }
        
        const cacheKey = this.generateCacheKey(req);
        const cachedResponse = this.cache.get(cacheKey);
        
        if (cachedResponse && !this.isCacheExpired(cachedResponse)) {
            // Serve from cache - only if headers haven't been sent yet
            if (!res.headersSent) {
                try {
                    res.setHeader('X-Cache', 'HIT');
                    res.setHeader('Content-Type', cachedResponse.contentType);
                    res.setHeader('Cache-Control', 'public, max-age=300');
                } catch (error) {
                    console.warn('Could not set cache headers:', error.message);
                }
            }
            return res.send(cachedResponse.data);
        }
        
        // Override res.send to cache response
        const originalSend = res.send;
        res.send = function(data) {
            // Cache successful responses
            if (res.statusCode === 200 && this.shouldCacheResponse(req, res)) {
                this.cacheResponse(cacheKey, data, res.getHeader('Content-Type'));
            }
            
            // Set cache header - only if headers haven't been sent yet
            if (!res.headersSent) {
                try {
                    res.setHeader('X-Cache', 'MISS');
                } catch (error) {
                    console.warn('Could not set X-Cache header:', error.message);
                }
            }
            
            return originalSend.call(res, data);
        }.bind(this); // Bind this to the function to access class methods
        
        next();
    }

    // Request optimization middleware
    requestOptimizationMiddleware(req, res, next) {
        // Add request ID for tracking
        req.id = this.generateRequestId();
        
        // Parse and optimize query parameters
        if (req.query) {
            req.query = this.optimizeQueryParams(req.query);
        }
        
        // Set optimal response headers - only if headers haven't been sent yet
        if (!res.headersSent) {
            try {
                res.setHeader('X-Powered-By', 'Naroop');
                res.setHeader('X-Frame-Options', 'DENY');
                res.setHeader('X-Content-Type-Options', 'nosniff');
            } catch (error) {
                console.warn('Could not set security headers:', error.message);
            }
        }
        
        next();
    }

    // Generate cache key for request
    generateCacheKey(req) {
        const url = req.originalUrl || req.url;
        const query = JSON.stringify(req.query || {});
        return `${req.method}:${url}:${query}`;
    }

    // Check if cache entry is expired
    isCacheExpired(cacheEntry) {
        return Date.now() - cacheEntry.timestamp > this.cacheConfig.ttl;
    }

    // Determine if response should be cached
    shouldCacheResponse(req, res) {
        // Don't cache large responses
        const contentLength = res.getHeader('Content-Length');
        if (contentLength && parseInt(contentLength) > 1024 * 1024) { // 1MB
            return false;
        }
        
        // Cache static resources and API responses
        const path = req.path;
        return path.startsWith('/api/') || 
               path.endsWith('.css') || 
               path.endsWith('.js') || 
               path.endsWith('.png') || 
               path.endsWith('.jpg') || 
               path.endsWith('.jpeg') || 
               path.endsWith('.gif') || 
               path.endsWith('.svg');
    }

    // Cache response data
    cacheResponse(key, data, contentType) {
        // Check cache size limit
        if (this.cache.size >= this.cacheConfig.maxSize) {
            this.cleanupOldCacheEntries();
        }
        
        this.cache.set(key, {
            data,
            contentType: contentType || 'application/json',
            timestamp: Date.now(),
            size: Buffer.byteLength(JSON.stringify(data))
        });
    }

    // Optimize query parameters
    optimizeQueryParams(query) {
        const optimized = {};
        
        for (const [key, value] of Object.entries(query)) {
            // Sanitize and optimize common parameters
            switch (key) {
                case 'limit':
                    optimized[key] = Math.min(parseInt(value) || 10, 100);
                    break;
                case 'offset':
                    optimized[key] = Math.max(parseInt(value) || 0, 0);
                    break;
                case 'page':
                    optimized[key] = Math.max(parseInt(value) || 1, 1);
                    break;
                case 'sort':
                    // Whitelist allowed sort fields
                    const allowedSorts = ['createdAt', 'updatedAt', 'title', 'likes'];
                    optimized[key] = allowedSorts.includes(value) ? value : 'createdAt';
                    break;
                case 'order':
                    optimized[key] = ['asc', 'desc'].includes(value) ? value : 'desc';
                    break;
                default:
                    optimized[key] = value;
            }
        }
        
        return optimized;
    }

    // Generate unique request ID
    generateRequestId() {
        return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // Update response time metrics
    updateResponseTimeMetrics(responseTime) {
        const total = this.requestMetrics.totalRequests;
        const current = this.requestMetrics.averageResponseTime;
        
        // Calculate rolling average
        this.requestMetrics.averageResponseTime = 
            ((current * (total - 1)) + responseTime) / total;
    }

    // Start cache cleanup interval
    startCleanupInterval() {
        setInterval(() => {
            this.cleanupOldCacheEntries();
            this.monitorMemoryUsage();
        }, this.cacheConfig.cleanupInterval);
    }

    // Clean up old cache entries
    cleanupOldCacheEntries() {
        const now = Date.now();
        let removedCount = 0;
        
        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > this.cacheConfig.ttl) {
                this.cache.delete(key);
                removedCount++;
            }
        }
        
        if (removedCount > 0) {
            console.log(`🧹 Cleaned up ${removedCount} expired cache entries`);
        }
    }

    // Monitor memory usage
    monitorMemoryUsage() {
        const memUsage = process.memoryUsage();
        
        if (memUsage.heapUsed > this.performanceThresholds.maxMemoryUsage) {
            console.warn(`⚠️ High memory usage: ${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`);
            
            // Force garbage collection if available
            if (global.gc) {
                global.gc();
                console.log('🧹 Forced garbage collection');
            }
            
            // Clear cache if memory is still high
            if (process.memoryUsage().heapUsed > this.performanceThresholds.maxMemoryUsage) {
                this.cache.clear();
                console.log('🧹 Cleared cache due to high memory usage');
            }
        }
    }

    // Get performance metrics
    getMetrics() {
        return {
            ...this.requestMetrics,
            cacheSize: this.cache.size,
            memoryUsage: process.memoryUsage(),
            uptime: process.uptime()
        };
    }

    // Clear all caches
    clearCache() {
        this.cache.clear();
        console.log('🧹 All caches cleared');
    }

    // Optimize database query
    optimizeQuery(query, options = {}) {
        const optimized = { ...query };
        
        // Add default limits
        if (!optimized.limit) {
            optimized.limit = options.defaultLimit || 20;
        }
        
        // Ensure reasonable limits
        optimized.limit = Math.min(optimized.limit, options.maxLimit || 100);
        
        // Add default sorting
        if (!optimized.sort) {
            optimized.sort = options.defaultSort || { createdAt: -1 };
        }
        
        return optimized;
    }
}

module.exports = BackendPerformanceOptimizer;
