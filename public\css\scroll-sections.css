/* Naroop Scroll Sections Styles */

.scroll-snap-container {
    height: 100vh;
    overflow-y: scroll;
    scroll-snap-type: y mandatory;
    scroll-behavior: smooth;
}

.section {
    height: 100vh;
    scroll-snap-align: start;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: var(--spacing-xl);
}

.section-content {
    max-width: 800px;
    text-align: center;
    z-index: 2;
    position: relative;
}

.section-heading {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-lg);
    line-height: var(--line-height-tight);
}

.section-subheading {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: var(--line-height-normal);
}

/* Section variants */
.section-gradient {
    background: var(--gradient-primary);
    color: white;
}

.section-gradient .section-subheading {
    color: rgba(255, 255, 255, 0.9);
}

.section-light {
    background: var(--surface-color);
    color: var(--text-primary);
}

.section-dark {
    background: var(--background-color);
    color: var(--text-primary);
}

.section-image {
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    position: relative;
}

.section-image::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1;
}

.section-image .section-content {
    color: white;
}

/* Feature cards */
.feature-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-xl);
}

.card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-lg);
    text-align: center;
    transition: transform var(--transition-normal);
}

.card:hover {
    transform: translateY(-5px);
}

.card-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-md);
}

.card h3 {
    margin-bottom: var(--spacing-sm);
    font-size: var(--font-size-lg);
}

.card p {
    color: var(--text-secondary);
    font-size: var(--font-size-sm);
}

/* Community showcase */
.community-showcase {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xl);
}

.community-stats {
    display: flex;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    color: var(--primary-color);
    margin-bottom: var(--spacing-xs);
}

.stat-label {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Navigation indicators */
.scroll-indicators {
    position: fixed;
    right: var(--spacing-lg);
    top: 50%;
    transform: translateY(-50%);
    z-index: var(--z-fixed);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.section-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.section-indicator.active {
    background: var(--primary-color);
    transform: scale(1.2);
}

.section-indicator:hover {
    background: rgba(255, 255, 255, 0.6);
}

/* Responsive design */
@media (max-width: 768px) {
    .section {
        padding: var(--spacing-lg) var(--spacing-md);
    }

    .section-heading {
        font-size: var(--font-size-3xl);
    }

    .section-subheading {
        font-size: var(--font-size-lg);
    }

    .feature-cards {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .community-stats {
        flex-direction: column;
        gap: var(--spacing-md);
    }

    .scroll-indicators {
        display: none;
    }

    .scroll-snap-container {
        scroll-snap-type: none;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    .scroll-snap-container {
        scroll-behavior: auto;
        scroll-snap-type: none;
    }

    .card {
        transition: none;
    }

    .card:hover {
        transform: none;
    }
}