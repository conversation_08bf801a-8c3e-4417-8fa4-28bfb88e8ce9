/* Naroop Forms Styles */

/* Form Container */
.form-container {
    max-width: 500px;
    margin: 0 auto;
    padding: var(--spacing-lg);
}

.auth-form {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

/* Form Groups */
.form-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.form-group label {
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    color: var(--text-primary);
    margin-bottom: var(--spacing-xs);
}

/* Input Styles */
.form-input,
input[type="text"],
input[type="email"],
input[type="password"],
input[type="tel"],
input[type="url"],
textarea,
select {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    font-family: var(--font-family-primary);
    transition: all var(--transition-fast);
}

.form-input:focus,
input:focus,
textarea:focus,
select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
    background: var(--surface-color);
}

.form-input:disabled,
input:disabled,
textarea:disabled,
select:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: var(--surface-color);
}

/* Textarea */
textarea {
    min-height: 100px;
    resize: vertical;
    font-family: var(--font-family-primary);
}

/* Select */
select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--spacing-sm) center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

/* Input Groups */
.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group-prepend,
.input-group-append {
    position: absolute;
    z-index: 2;
    display: flex;
    align-items: center;
    padding: 0 var(--spacing-sm);
    color: var(--text-muted);
    font-size: var(--font-size-sm);
}

.input-group-prepend {
    left: 0;
}

.input-group-append {
    right: 0;
}

.input-group .form-input {
    padding-left: 2.5rem;
}

.input-group.has-append .form-input {
    padding-right: 2.5rem;
}

/* Field States */
.field-error,
.field-success,
.field-warning {
    font-size: var(--font-size-xs);
    margin-top: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
}

.field-error {
    color: var(--error-color);
}

.field-success {
    color: var(--success-color);
}

.field-warning {
    color: var(--warning-color);
}

.form-input.error,
input.error {
    border-color: var(--error-color);
    box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
}

.form-input.success,
input.success {
    border-color: var(--success-color);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-input.warning,
input.warning {
    border-color: var(--warning-color);
    box-shadow: 0 0 0 3px rgba(255, 152, 0, 0.1);
}

/* Checkboxes and Radio Buttons */
.checkbox-group,
.radio-group {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    cursor: pointer;
}

.checkbox,
.radio {
    position: relative;
    width: 20px;
    height: 20px;
    margin: 0;
    cursor: pointer;
}

.checkbox input,
.radio input {
    position: absolute;
    opacity: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    cursor: pointer;
}

.checkbox-custom,
.radio-custom {
    position: absolute;
    top: 0;
    left: 0;
    width: 20px;
    height: 20px;
    background: var(--card-background);
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
}

.radio-custom {
    border-radius: 50%;
}

.checkbox input:checked + .checkbox-custom,
.radio input:checked + .radio-custom {
    background: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox input:checked + .checkbox-custom::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.radio input:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: white;
    border-radius: 50%;
}

/* Form Buttons */
.form-buttons {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    margin-top: var(--spacing-lg);
}

.auth-btn {
    width: 100%;
    padding: var(--spacing-md);
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-weight: var(--font-weight-medium);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.auth-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.auth-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Form Validation */
.form-validation {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-md);
}

.validation-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.validation-item.valid {
    color: var(--success-color);
}

.validation-item.invalid {
    color: var(--error-color);
}

/* Loading States */
.form-loading {
    position: relative;
    pointer-events: none;
}

.form-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.1);
    border-radius: var(--border-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Auth Modal Specific */
.auth-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.auth-modal.show {
    opacity: 1;
    visibility: visible;
}

.auth-modal-content {
    background: var(--card-background);
    border-radius: var(--border-radius-lg);
    width: 90%;
    max-width: 400px;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.auth-modal.show .auth-modal-content {
    transform: scale(1);
}

.auth-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
}

.auth-modal-body {
    padding: var(--spacing-lg);
}

.close-btn {
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    color: var(--text-muted);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
}

.close-btn:hover {
    background: var(--surface-color);
    color: var(--text-primary);
}

.auth-switch {
    text-align: center;
    margin-top: var(--spacing-md);
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

.auth-switch a {
    color: var(--primary-color);
    text-decoration: none;
    font-weight: var(--font-weight-medium);
}

.auth-switch a:hover {
    text-decoration: underline;
}

.auth-error {
    background: rgba(244, 67, 54, 0.1);
    border: 1px solid var(--error-color);
    color: var(--error-color);
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-sm);
}

.auth-loading {
    text-align: center;
    padding: var(--spacing-lg);
}

.auth-loading .loading-spinner {
    margin-bottom: var(--spacing-md);
}
