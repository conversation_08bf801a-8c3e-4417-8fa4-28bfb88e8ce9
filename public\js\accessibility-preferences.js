// Accessibility Preferences Module for Naroop
// Handles accessibility settings and user preferences

class AccessibilityPreferences {
    constructor() {
        this.preferences = {
            theme: 'auto', // auto, light, dark, high-contrast
            fontSize: 'medium', // small, medium, large, extra-large
            reducedMotion: false,
            highContrast: false,
            screenReader: false,
            keyboardNavigation: true
        };
        
        this.init();
    }

    init() {
        this.loadPreferences();
        this.applyPreferences();
        this.setupEventHandlers();
        this.setupKeyboardNavigation();
        this.detectSystemPreferences();
    }

    loadPreferences() {
        const stored = localStorage.getItem('naroop_accessibility_preferences');
        if (stored) {
            try {
                this.preferences = { ...this.preferences, ...JSON.parse(stored) };
            } catch (error) {
                console.warn('Failed to load accessibility preferences:', error);
            }
        }
    }

    savePreferences() {
        try {
            localStorage.setItem('naroop_accessibility_preferences', JSON.stringify(this.preferences));
        } catch (error) {
            console.warn('Failed to save accessibility preferences:', error);
        }
    }

    applyPreferences() {
        this.applyTheme();
        this.applyFontSize();
        this.applyMotionPreference();
        this.applyHighContrast();
        this.updatePreferencesUI();
    }

    applyTheme() {
        const { theme } = this.preferences;
        const root = document.documentElement;
        
        // Remove existing theme classes
        root.classList.remove('theme-light', 'theme-dark', 'theme-high-contrast');
        
        if (theme === 'light') {
            root.setAttribute('data-theme', 'light');
        } else if (theme === 'dark') {
            root.setAttribute('data-theme', 'dark');
        } else if (theme === 'high-contrast') {
            root.setAttribute('data-theme', 'high-contrast');
        } else {
            // Auto theme - use system preference
            root.removeAttribute('data-theme');
        }
    }

    applyFontSize() {
        const { fontSize } = this.preferences;
        const root = document.documentElement;
        
        const fontSizeMap = {
            'small': '14px',
            'medium': '16px',
            'large': '18px',
            'extra-large': '20px'
        };
        
        root.style.fontSize = fontSizeMap[fontSize] || '16px';
    }

    applyMotionPreference() {
        const { reducedMotion } = this.preferences;
        const root = document.documentElement;
        
        if (reducedMotion) {
            root.style.setProperty('--transition-fast', '0.01ms');
            root.style.setProperty('--transition-normal', '0.01ms');
            root.style.setProperty('--transition-slow', '0.01ms');
        } else {
            root.style.removeProperty('--transition-fast');
            root.style.removeProperty('--transition-normal');
            root.style.removeProperty('--transition-slow');
        }
    }

    applyHighContrast() {
        const { highContrast } = this.preferences;
        const root = document.documentElement;
        
        if (highContrast) {
            root.setAttribute('data-theme', 'high-contrast');
        }
    }

    detectSystemPreferences() {
        // Detect system dark mode preference
        if (window.matchMedia) {
            const darkModeQuery = window.matchMedia('(prefers-color-scheme: dark)');
            darkModeQuery.addEventListener('change', () => {
                if (this.preferences.theme === 'auto') {
                    this.applyTheme();
                }
            });

            // Detect reduced motion preference
            const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
            if (reducedMotionQuery.matches && !localStorage.getItem('naroop_accessibility_preferences')) {
                this.preferences.reducedMotion = true;
                this.applyMotionPreference();
            }

            // Detect high contrast preference
            const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
            if (highContrastQuery.matches && !localStorage.getItem('naroop_accessibility_preferences')) {
                this.preferences.highContrast = true;
                this.applyHighContrast();
            }
        }
    }

    setupEventHandlers() {
        // Handle preference changes
        document.addEventListener('change', (e) => {
            if (e.target.matches('[data-preference]')) {
                this.handlePreferenceChange(e);
            }
        });

        // Handle preference buttons
        document.addEventListener('click', (e) => {
            if (e.target.matches('[data-preference-action]')) {
                this.handlePreferenceAction(e);
            }
        });
    }

    handlePreferenceChange(event) {
        const input = event.target;
        const preference = input.dataset.preference;
        const value = input.type === 'checkbox' ? input.checked : input.value;
        
        this.updatePreference(preference, value);
    }

    handlePreferenceAction(event) {
        const button = event.target;
        const action = button.dataset.preferenceAction;
        
        switch (action) {
            case 'reset-preferences':
                this.resetPreferences();
                break;
            case 'toggle-theme':
                this.toggleTheme();
                break;
            case 'increase-font':
                this.adjustFontSize(1);
                break;
            case 'decrease-font':
                this.adjustFontSize(-1);
                break;
        }
    }

    updatePreference(key, value) {
        this.preferences[key] = value;
        this.savePreferences();
        this.applyPreferences();
        
        // Announce change to screen readers
        this.announcePreferenceChange(key, value);
    }

    toggleTheme() {
        const themes = ['auto', 'light', 'dark', 'high-contrast'];
        const currentIndex = themes.indexOf(this.preferences.theme);
        const nextIndex = (currentIndex + 1) % themes.length;
        
        this.updatePreference('theme', themes[nextIndex]);
    }

    adjustFontSize(direction) {
        const sizes = ['small', 'medium', 'large', 'extra-large'];
        const currentIndex = sizes.indexOf(this.preferences.fontSize);
        const newIndex = Math.max(0, Math.min(sizes.length - 1, currentIndex + direction));
        
        this.updatePreference('fontSize', sizes[newIndex]);
    }

    resetPreferences() {
        this.preferences = {
            theme: 'auto',
            fontSize: 'medium',
            reducedMotion: false,
            highContrast: false,
            screenReader: false,
            keyboardNavigation: true
        };
        
        this.savePreferences();
        this.applyPreferences();
        this.announcePreferenceChange('all', 'reset');
    }

    updatePreferencesUI() {
        // Update theme selector
        const themeSelect = document.querySelector('[data-preference="theme"]');
        if (themeSelect) {
            themeSelect.value = this.preferences.theme;
        }

        // Update font size selector
        const fontSizeSelect = document.querySelector('[data-preference="fontSize"]');
        if (fontSizeSelect) {
            fontSizeSelect.value = this.preferences.fontSize;
        }

        // Update checkboxes
        Object.keys(this.preferences).forEach(key => {
            const checkbox = document.querySelector(`[data-preference="${key}"][type="checkbox"]`);
            if (checkbox) {
                checkbox.checked = this.preferences[key];
            }
        });
    }

    setupKeyboardNavigation() {
        if (!this.preferences.keyboardNavigation) return;

        // Add keyboard navigation support
        document.addEventListener('keydown', (e) => {
            // Skip if user is typing in an input
            if (e.target.matches('input, textarea, select')) return;

            switch (e.key) {
                case 'Tab':
                    this.handleTabNavigation(e);
                    break;
                case 'Enter':
                case ' ':
                    this.handleActivation(e);
                    break;
                case 'Escape':
                    this.handleEscape(e);
                    break;
                case 'ArrowUp':
                case 'ArrowDown':
                case 'ArrowLeft':
                case 'ArrowRight':
                    this.handleArrowNavigation(e);
                    break;
            }
        });

        // Add focus indicators
        this.addFocusIndicators();
    }

    handleTabNavigation(event) {
        // Ensure proper tab order and focus management
        const focusableElements = this.getFocusableElements();
        const currentIndex = focusableElements.indexOf(document.activeElement);
        
        if (event.shiftKey) {
            // Shift+Tab - go backwards
            if (currentIndex <= 0) {
                event.preventDefault();
                focusableElements[focusableElements.length - 1].focus();
            }
        } else {
            // Tab - go forwards
            if (currentIndex >= focusableElements.length - 1) {
                event.preventDefault();
                focusableElements[0].focus();
            }
        }
    }

    handleActivation(event) {
        const element = event.target;
        
        // Activate buttons and links with Enter/Space
        if (element.matches('button, [role="button"], a')) {
            if (event.key === ' ') {
                event.preventDefault();
            }
            element.click();
        }
    }

    handleEscape(event) {
        // Close modals, dropdowns, etc.
        const modal = document.querySelector('.modal-overlay');
        if (modal && window.modalSystem) {
            window.modalSystem.closeModal();
        }
    }

    handleArrowNavigation(event) {
        // Handle arrow key navigation for specific components
        const element = event.target;
        
        if (element.matches('.nav-item')) {
            this.navigateNavItems(event);
        }
    }

    navigateNavItems(event) {
        const navItems = Array.from(document.querySelectorAll('.nav-item'));
        const currentIndex = navItems.indexOf(event.target);
        let nextIndex;

        switch (event.key) {
            case 'ArrowUp':
                nextIndex = currentIndex > 0 ? currentIndex - 1 : navItems.length - 1;
                break;
            case 'ArrowDown':
                nextIndex = currentIndex < navItems.length - 1 ? currentIndex + 1 : 0;
                break;
            default:
                return;
        }

        event.preventDefault();
        navItems[nextIndex].focus();
    }

    getFocusableElements() {
        return Array.from(document.querySelectorAll(
            'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        )).filter(el => {
            return !el.disabled && 
                   !el.hidden && 
                   el.offsetParent !== null &&
                   getComputedStyle(el).visibility !== 'hidden';
        });
    }

    addFocusIndicators() {
        // Add CSS for focus indicators if not already present
        if (!document.querySelector('#accessibility-focus-styles')) {
            const style = document.createElement('style');
            style.id = 'accessibility-focus-styles';
            style.textContent = `
                .focus-visible,
                *:focus-visible {
                    outline: 2px solid var(--primary-color) !important;
                    outline-offset: 2px !important;
                }
                
                .skip-link {
                    position: absolute;
                    top: -40px;
                    left: 6px;
                    background: var(--primary-color);
                    color: white;
                    padding: 8px;
                    text-decoration: none;
                    border-radius: 4px;
                    z-index: 10000;
                }
                
                .skip-link:focus {
                    top: 6px;
                }
            `;
            document.head.appendChild(style);
        }

        // Add skip link if not present
        if (!document.querySelector('.skip-link')) {
            const skipLink = document.createElement('a');
            skipLink.href = '#main-content';
            skipLink.className = 'skip-link';
            skipLink.textContent = 'Skip to main content';
            document.body.insertBefore(skipLink, document.body.firstChild);
        }
    }

    announcePreferenceChange(key, value) {
        // Create announcement for screen readers
        const announcement = document.createElement('div');
        announcement.setAttribute('aria-live', 'polite');
        announcement.setAttribute('aria-atomic', 'true');
        announcement.className = 'sr-only';
        
        let message = '';
        if (key === 'all') {
            message = 'All accessibility preferences have been reset to default values.';
        } else {
            message = `${key} preference changed to ${value}.`;
        }
        
        announcement.textContent = message;
        document.body.appendChild(announcement);
        
        // Remove after announcement
        setTimeout(() => {
            document.body.removeChild(announcement);
        }, 1000);
    }

    // Public methods
    getPreferences() {
        return { ...this.preferences };
    }

    setPreference(key, value) {
        this.updatePreference(key, value);
    }

    isHighContrastMode() {
        return this.preferences.highContrast || this.preferences.theme === 'high-contrast';
    }

    isReducedMotionMode() {
        return this.preferences.reducedMotion;
    }
}

// Create and initialize accessibility preferences
const accessibilityPreferences = new AccessibilityPreferences();

// Export for use in other modules
window.AccessibilityPreferences = AccessibilityPreferences;
window.accessibilityPreferences = accessibilityPreferences;
