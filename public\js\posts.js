// Posts Module for Naroop
// Handles post creation, display, and interaction

import { naroopCore } from './core.js';
import { authManager } from './authentication.js';

class PostsManager {
    constructor() {
        this.posts = [];
        this.isLoading = false;
        this.init();
    }

    init() {
        this.setupEventHandlers();
        this.loadPosts();
        
        // Register with core
        naroopCore.registerModule('posts', this);
    }

    setupEventHandlers() {
        // Handle post creation
        document.addEventListener('click', (e) => {
            if (e.target.matches('.create-post-btn, .create-post-btn *')) {
                e.preventDefault();
                this.showCreatePostModal();
            }
            
            if (e.target.matches('.post-action, .post-action *')) {
                e.preventDefault();
                this.handlePostAction(e);
            }
        });

        // Handle post form submission
        document.addEventListener('submit', (e) => {
            if (e.target.matches('.create-post-form')) {
                e.preventDefault();
                this.handleCreatePost(e);
            }
        });
    }

    async loadPosts() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoadingState();

        try {
            // In a real app, this would fetch from the server
            // For now, we'll use mock data or empty state
            const response = await fetch('/api/posts');
            
            if (response.ok) {
                this.posts = await response.json();
            } else {
                // Show empty state for new users
                this.posts = [];
            }
            
            this.renderPosts();
            
        } catch (error) {
            console.error('Error loading posts:', error);
            this.showErrorState();
        } finally {
            this.isLoading = false;
            this.hideLoadingState();
        }
    }

    renderPosts() {
        const postsContainer = document.querySelector('.posts-container');
        if (!postsContainer) return;

        if (this.posts.length === 0) {
            this.showEmptyState(postsContainer);
            return;
        }

        const postsHTML = this.posts.map(post => this.renderPost(post)).join('');
        postsContainer.innerHTML = postsHTML;
    }

    renderPost(post) {
        const timeAgo = this.getTimeAgo(post.createdAt);
        const userInitials = this.getUserInitials(post.author.name);
        
        return `
            <article class="post" data-post-id="${post.id}">
                <div class="post-header">
                    <div class="post-avatar">${userInitials}</div>
                    <div class="post-meta">
                        <div class="post-author">${post.author.name}</div>
                        <div class="post-time">${timeAgo}</div>
                    </div>
                </div>
                <div class="post-content">
                    <p>${post.content}</p>
                    ${post.image ? `<img src="${post.image}" alt="Post image" class="post-image">` : ''}
                </div>
                <div class="post-actions">
                    <button class="post-action" data-action="like" data-post-id="${post.id}">
                        <span class="icon">❤️</span>
                        <span class="count">${post.likes || 0}</span>
                    </button>
                    <button class="post-action" data-action="comment" data-post-id="${post.id}">
                        <span class="icon">💬</span>
                        <span class="count">${post.comments || 0}</span>
                    </button>
                    <button class="post-action" data-action="share" data-post-id="${post.id}">
                        <span class="icon">🔗</span>
                        <span>Share</span>
                    </button>
                </div>
            </article>
        `;
    }

    showEmptyState(container) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-state-icon">📝</div>
                <h3>No posts yet</h3>
                <p>Be the first to share a positive story with the community!</p>
                <button class="btn-base btn-primary create-post-btn">
                    Create Your First Post
                </button>
            </div>
        `;
    }

    showLoadingState() {
        const postsContainer = document.querySelector('.posts-container');
        if (postsContainer) {
            postsContainer.innerHTML = `
                <div class="loading-skeleton"></div>
                <div class="loading-skeleton"></div>
                <div class="loading-skeleton"></div>
            `;
        }
    }

    hideLoadingState() {
        const loadingElements = document.querySelectorAll('.loading-skeleton');
        loadingElements.forEach(el => el.remove());
    }

    showErrorState() {
        const postsContainer = document.querySelector('.posts-container');
        if (postsContainer) {
            postsContainer.innerHTML = `
                <div class="error-state">
                    <div class="error-icon">⚠️</div>
                    <h3>Unable to load posts</h3>
                    <p>Please check your connection and try again.</p>
                    <button class="btn-base btn-secondary" onclick="location.reload()">
                        Retry
                    </button>
                </div>
            `;
        }
    }

    showCreatePostModal() {
        if (!authManager.isAuthenticated()) {
            // Show sign in modal instead
            window.modalSystem?.showAuthModal();
            return;
        }

        const modalHTML = `
            <div class="modal-overlay">
                <div class="modal-content create-post-modal">
                    <div class="modal-header">
                        <h2>Share Your Story</h2>
                        <button class="modal-close-button">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form class="create-post-form">
                            <div class="form-group">
                                <textarea 
                                    class="form-input post-content-input" 
                                    placeholder="Share something positive with the community..."
                                    rows="4"
                                    maxlength="500"
                                    required
                                ></textarea>
                                <div class="character-count">0/500</div>
                            </div>
                            <div class="form-group">
                                <label for="post-image">Add an image (optional)</label>
                                <input type="file" id="post-image" accept="image/*" class="form-input">
                            </div>
                            <div class="form-buttons">
                                <button type="button" class="btn-base btn-secondary modal-close">Cancel</button>
                                <button type="submit" class="btn-base btn-primary">Share Story</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        `;

        // Use modal system if available
        if (window.modalSystem) {
            window.modalSystem.displayModal(modalHTML);
        } else {
            // Fallback: add to body directly
            const modalElement = document.createElement('div');
            modalElement.innerHTML = modalHTML;
            document.body.appendChild(modalElement.firstElementChild);
        }

        // Set up character counter
        const textarea = document.querySelector('.post-content-input');
        const counter = document.querySelector('.character-count');
        
        if (textarea && counter) {
            textarea.addEventListener('input', () => {
                const length = textarea.value.length;
                counter.textContent = `${length}/500`;
                
                if (length > 450) {
                    counter.style.color = 'var(--warning-color)';
                } else {
                    counter.style.color = 'var(--text-muted)';
                }
            });
        }
    }

    async handleCreatePost(event) {
        const form = event.target;
        const formData = new FormData(form);
        const content = formData.get('content') || form.querySelector('.post-content-input').value;
        
        if (!content.trim()) {
            alert('Please enter some content for your post.');
            return;
        }

        try {
            const postData = {
                content: content.trim(),
                author: {
                    name: authManager.getUserDisplayName(),
                    id: authManager.getCurrentUser()?.uid
                },
                createdAt: new Date().toISOString()
            };

            // In a real app, this would send to the server
            const response = await fetch('/api/posts', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(postData)
            });

            if (response.ok) {
                const newPost = await response.json();
                this.posts.unshift(newPost);
                this.renderPosts();
                
                // Close modal
                if (window.modalSystem) {
                    window.modalSystem.closeModal();
                }
                
                // Show success message
                this.showSuccessMessage('Your story has been shared!');
                
            } else {
                throw new Error('Failed to create post');
            }
            
        } catch (error) {
            console.error('Error creating post:', error);
            alert('Failed to share your story. Please try again.');
        }
    }

    handlePostAction(event) {
        const button = event.target.closest('.post-action');
        const action = button.dataset.action;
        const postId = button.dataset.postId;
        
        switch (action) {
            case 'like':
                this.toggleLike(postId, button);
                break;
            case 'comment':
                this.showComments(postId);
                break;
            case 'share':
                this.sharePost(postId);
                break;
        }
    }

    async toggleLike(postId, button) {
        if (!authManager.isAuthenticated()) {
            window.modalSystem?.showAuthModal();
            return;
        }

        try {
            const response = await fetch(`/api/posts/${postId}/like`, {
                method: 'POST'
            });
            
            if (response.ok) {
                const result = await response.json();
                const countElement = button.querySelector('.count');
                if (countElement) {
                    countElement.textContent = result.likes;
                }
                
                button.classList.toggle('active', result.liked);
            }
        } catch (error) {
            console.error('Error toggling like:', error);
        }
    }

    showComments(postId) {
        console.log('Show comments for post:', postId);
        // TODO: Implement comments modal
    }

    sharePost(postId) {
        if (navigator.share) {
            navigator.share({
                title: 'Check out this story on Naroop',
                url: `${window.location.origin}/post/${postId}`
            });
        } else {
            // Fallback: copy to clipboard
            const url = `${window.location.origin}/post/${postId}`;
            navigator.clipboard.writeText(url).then(() => {
                this.showSuccessMessage('Link copied to clipboard!');
            });
        }
    }

    showSuccessMessage(message) {
        // Simple success notification
        const notification = document.createElement('div');
        notification.className = 'notification success';
        notification.textContent = message;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // Utility methods
    getTimeAgo(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diffInSeconds = Math.floor((now - date) / 1000);
        
        if (diffInSeconds < 60) return 'Just now';
        if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
        if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
        return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }

    getUserInitials(name) {
        if (!name) return 'U';
        const names = name.split(' ');
        if (names.length >= 2) {
            return (names[0][0] + names[1][0]).toUpperCase();
        }
        return name[0].toUpperCase();
    }
}

// Create and export posts manager instance
const postsManager = new PostsManager();

// Export for use in other modules
export { PostsManager, postsManager };
export default postsManager;
