<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop - Connect & Share Your Story</title>

    <!-- External CSS Files -->
    <link rel="stylesheet" href="/public/css/variables.css?v=3">
    <link rel="stylesheet" href="/public/css/main.css?v=3">
    <link rel="stylesheet" href="/public/css/modern-animations.css?v=1">
    <link rel="stylesheet" href="/public/css/responsive.css">
    <link rel="stylesheet" href="/public/css/forms.css">
    <link rel="stylesheet" href="/public/css/notifications.css">
    <link rel="stylesheet" href="/public/css/landing-specific.css?v=1">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <div class="header-content">
            <div class="logo">Naroop</div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-content">
            <h1 class="hero-title">Connect & Share</h1>
            <p class="hero-subtitle">A place where our community comes together to share positive experiences, inspire each other, and build meaningful connections.</p>
            <div class="hero-buttons fade-in-up">
                <button class="hero-btn btn-base btn-primary btn-lg hover-lift focus-ring">Join Our Community</button>
                <button class="hero-btn btn-base btn-secondary btn-lg hover-lift focus-ring">Sign In</button>
            </div>
        </div>
    </section>

    <!-- Authentication Modal -->
    <div id="authModal" class="auth-modal">
        <div class="auth-modal-content">
            <div class="auth-modal-header">
                <h3 id="authModalTitle">Welcome to Naroop</h3>
                <button id="closeAuthModal" class="close-btn">&times;</button>
            </div>
            <div class="auth-modal-body">
                <!-- Sign In Form -->
                <div id="signInForm" class="auth-form">
                    <div class="form-group">
                        <label for="signInEmail">Email</label>
                        <input type="email" id="signInEmail" placeholder="Enter your email" required>
                        <div class="field-error" id="signInEmailError"></div>
                        <div class="field-success" id="signInEmailSuccess"></div>
                    </div>
                    <div class="form-group">
                        <label for="signInPassword">Password</label>
                        <input type="password" id="signInPassword" placeholder="Enter your password" required>
                        <div class="field-error" id="signInPasswordError"></div>
                        <div class="field-success" id="signInPasswordSuccess"></div>
                    </div>
                    <button id="signInBtn" class="auth-btn">Sign In</button>
                    <p class="auth-switch">Don't have an account? <a href="#" id="showSignUpForm">Join our community</a></p>
                </div>

                <!-- Sign Up Form -->
                <div id="signUpForm" class="auth-form" style="display: none;">
                    <div class="form-group">
                        <label for="signUpUsername">Username</label>
                        <input type="text" id="signUpUsername" placeholder="Choose a username" required>
                        <div class="field-error" id="signUpUsernameError"></div>
                        <div class="field-success" id="signUpUsernameSuccess"></div>
                    </div>
                    <div class="form-group">
                        <label for="signUpEmail">Email</label>
                        <input type="email" id="signUpEmail" placeholder="Enter your email" required>
                        <div class="field-error" id="signUpEmailError"></div>
                        <div class="field-success" id="signUpEmailSuccess"></div>
                    </div>
                    <div class="form-group">
                        <label for="signUpPassword">Password</label>
                        <input type="password" id="signUpPassword" placeholder="Create a password" required>
                        <div class="field-error" id="signUpPasswordError"></div>
                        <div class="field-success" id="signUpPasswordSuccess"></div>
                    </div>
                    <button id="signUpBtn" class="auth-btn">Join Our Community</button>
                    <p class="auth-switch">Already have an account? <a href="#" id="showSignInForm">Sign in</a></p>
                </div>

                <div id="authError" class="auth-error" style="display: none;"></div>
                <div id="authLoading" class="auth-loading" style="display: none;">
                    <div class="loading-spinner"></div>
                    <p>Please wait...</p>
                </div>
            </div>
        </div>
    </div>

    <script type="module">
        // Import Firebase authentication
        import { FirebaseAuth } from './public/js/firebase-config.js';

        // Initialize Firebase when page loads
        let firebaseInitialized = false;
        let redirectInProgress = false;

        document.addEventListener('DOMContentLoaded', async function() {
            // Initialize Firebase first
            try {
                firebaseInitialized = await FirebaseAuth.init();
                if (firebaseInitialized) {
                    console.log('Firebase initialized successfully');
                } else {
                    console.error('Firebase initialization failed - authentication unavailable');
                    showError('Authentication service is unavailable. Please try again later.');
                }
            } catch (error) {
                console.error('Error initializing Firebase:', error);
                firebaseInitialized = false;
            }

            // Check if user is already authenticated with better error handling
            let isAuthenticated = false;

            // Always check localStorage first
            const currentUser = localStorage.getItem('currentUser');
            const authToken = localStorage.getItem('authToken');

            if (currentUser && authToken) {
                if (!redirectInProgress) {
                    redirectInProgress = true;
                    console.log('Found stored authentication, redirecting to main page');
                    window.location.href = '/index.html';
                }
                return;
            }

            // If Firebase is available, also check Firebase session
            if (firebaseInitialized) {
                try {
                    const sessionValidation = await FirebaseAuth.validateSession();
                    if (sessionValidation.valid) {
                        console.log('Valid Firebase session found, redirecting to main page');
                        window.location.href = '/index.html';
                        return;
                    }
                } catch (error) {
                    console.warn('Firebase session validation error on landing page:', error);
                    // Don't redirect on error, just continue to show login form
                }
            }

            // Set up authentication state monitoring with delay
            if (firebaseInitialized) {
                // Listen for auth state changes with a delay to prevent immediate redirects
                setTimeout(() => {
                    FirebaseAuth.onAuthStateChanged((user) => {
                        if (user && !redirectInProgress) {
                            // Only redirect if user is actually authenticated and we haven't just logged out
                            // Check if this is a legitimate sign-in (not a leftover auth state)
                            const hasValidSession = localStorage.getItem('currentUser') && localStorage.getItem('authToken');

                            if (hasValidSession) {
                                console.log('Auth state changed: user signed in with valid session, redirecting');
                                redirectInProgress = true;
                                window.location.href = '/index.html';
                            } else {
                                console.log('Auth state changed: user detected but no valid session, clearing Firebase auth');
                                // Clear the Firebase auth state if we don't have a valid local session
                                FirebaseAuth.signOut().then(() => {
                                    console.log('Cleared stale Firebase auth state');
                                }).catch(error => {
                                    console.warn('Error clearing stale auth state:', error);
                                });
                            }
                        } else if (!user) {
                            console.log('Auth state changed: user signed out');
                            redirectInProgress = false;
                        }
                    });
                }, 2000); // Wait 2 seconds before setting up the listener to allow logout to complete
            }
        });

        // Smooth scroll for logo click
        document.querySelector('.logo').addEventListener('click', function() {
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });

        // Add scroll effect to header
        window.addEventListener('scroll', function() {
            const header = document.querySelector('.header');
            if (window.scrollY > 50) {
                header.style.background = 'rgba(255, 255, 255, 0.98)';
                header.style.boxShadow = '0 2px 20px rgba(0, 0, 0, 0.1)';
            } else {
                header.style.background = 'rgba(255, 255, 255, 0.95)';
                header.style.boxShadow = 'none';
            }
        });

        // Modal functionality
        const authModal = document.getElementById('authModal');
        const closeAuthModal = document.getElementById('closeAuthModal');
        const signInForm = document.getElementById('signInForm');
        const signUpForm = document.getElementById('signUpForm');
        const showSignUpForm = document.getElementById('showSignUpForm');
        const showSignInForm = document.getElementById('showSignInForm');
        const authModalTitle = document.getElementById('authModalTitle');
        const authError = document.getElementById('authError');
        const authLoading = document.getElementById('authLoading');

        // Button click handlers
        document.querySelectorAll('.hero-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.textContent.trim() === 'Join Our Community') {
                    openModal('signup');
                } else if (this.textContent.trim() === 'Sign In') {
                    openModal('signin');
                }
            });
        });

        function openModal(mode) {
            authModal.style.display = 'flex';
            hideError();
            hideLoading();

            if (mode === 'signup') {
                showSignUpFormView();
            } else {
                showSignInFormView();
            }
        }

        function closeModal() {
            authModal.style.display = 'none';
            clearForms();
        }

        function showSignInFormView() {
            signInForm.style.display = 'block';
            signUpForm.style.display = 'none';
            authModalTitle.textContent = 'Welcome Back';
        }

        function showSignUpFormView() {
            signInForm.style.display = 'none';
            signUpForm.style.display = 'block';
            authModalTitle.textContent = 'Join Our Community';
        }

        function showError(message) {
            authError.textContent = message;
            authError.style.display = 'block';
        }

        function hideError() {
            authError.style.display = 'none';
        }

        function showLoading() {
            authLoading.style.display = 'block';
        }

        function hideLoading() {
            authLoading.style.display = 'none';
        }

        function clearForms() {
            document.getElementById('signInEmail').value = '';
            document.getElementById('signInPassword').value = '';
            document.getElementById('signUpUsername').value = '';
            document.getElementById('signUpEmail').value = '';
            document.getElementById('signUpPassword').value = '';
            hideError();
            hideLoading();
            clearFieldValidation();
        }

        function clearFieldValidation() {
            // Clear all field validation states
            const inputs = document.querySelectorAll('.form-group input');
            const errors = document.querySelectorAll('.field-error');
            const successes = document.querySelectorAll('.field-success');

            inputs.forEach(input => {
                input.classList.remove('error', 'success');
            });

            errors.forEach(error => {
                error.style.display = 'none';
                error.textContent = '';
            });

            successes.forEach(success => {
                success.style.display = 'none';
                success.textContent = '';
            });
        }

        function showFieldError(fieldId, message) {
            const input = document.getElementById(fieldId);
            const errorElement = document.getElementById(fieldId + 'Error');
            const successElement = document.getElementById(fieldId + 'Success');

            input.classList.remove('success');
            input.classList.add('error');

            if (errorElement) {
                errorElement.textContent = message;
                errorElement.style.display = 'block';
            }

            if (successElement) {
                successElement.style.display = 'none';
            }
        }

        function showFieldSuccess(fieldId, message = '') {
            const input = document.getElementById(fieldId);
            const errorElement = document.getElementById(fieldId + 'Error');
            const successElement = document.getElementById(fieldId + 'Success');

            input.classList.remove('error');
            input.classList.add('success');

            if (errorElement) {
                errorElement.style.display = 'none';
            }

            if (successElement && message) {
                successElement.textContent = message;
                successElement.style.display = 'block';
            }
        }

        function clearFieldValidationState(fieldId) {
            const input = document.getElementById(fieldId);
            const errorElement = document.getElementById(fieldId + 'Error');
            const successElement = document.getElementById(fieldId + 'Success');

            input.classList.remove('error', 'success');

            if (errorElement) {
                errorElement.style.display = 'none';
            }

            if (successElement) {
                successElement.style.display = 'none';
            }
        }

        // Event listeners
        closeAuthModal.addEventListener('click', closeModal);
        showSignUpForm.addEventListener('click', (e) => {
            e.preventDefault();
            showSignUpFormView();
        });
        showSignInForm.addEventListener('click', (e) => {
            e.preventDefault();
            showSignInFormView();
        });

        // Close modal when clicking outside
        authModal.addEventListener('click', (e) => {
            if (e.target === authModal) {
                closeModal();
            }
        });

        // Real-time validation for sign-up form
        if (firebaseInitialized) {
            document.getElementById('signUpUsername').addEventListener('input', function() {
                const username = this.value.trim();
                if (username) {
                    const validation = FirebaseAuth.validateUsername(username);
                    if (validation.isValid) {
                        showFieldSuccess('signUpUsername', '✓ Valid username');
                    } else {
                        showFieldError('signUpUsername', validation.errors[0]);
                    }
                } else {
                    clearFieldValidationState('signUpUsername');
                }
            });

            document.getElementById('signUpEmail').addEventListener('input', function() {
                const email = this.value.trim();
                if (email) {
                    if (FirebaseAuth.validateEmail(email)) {
                        showFieldSuccess('signUpEmail', '✓ Valid email');
                    } else {
                        showFieldError('signUpEmail', 'Please enter a valid email address');
                    }
                } else {
                    clearFieldValidationState('signUpEmail');
                }
            });

            document.getElementById('signUpPassword').addEventListener('input', function() {
                const password = this.value;
                if (password) {
                    const validation = FirebaseAuth.validatePassword(password);
                    if (validation.isValid) {
                        showFieldSuccess('signUpPassword', '✓ Strong password');
                    } else {
                        showFieldError('signUpPassword', validation.errors[0]);
                    }
                } else {
                    clearFieldValidationState('signUpPassword');
                }
            });

            document.getElementById('signInEmail').addEventListener('input', function() {
                const email = this.value.trim();
                if (email) {
                    if (FirebaseAuth.validateEmail(email)) {
                        showFieldSuccess('signInEmail', '✓ Valid email');
                    } else {
                        showFieldError('signInEmail', 'Please enter a valid email address');
                    }
                } else {
                    clearFieldValidationState('signInEmail');
                }
            });

            document.getElementById('signInPassword').addEventListener('input', function() {
                const password = this.value;
                if (password) {
                    clearFieldValidationState('signInPassword');
                } else {
                    clearFieldValidationState('signInPassword');
                }
            });
        }

        // Sign In functionality
        document.getElementById('signInBtn').addEventListener('click', async function() {
            const email = document.getElementById('signInEmail').value.trim();
            const password = document.getElementById('signInPassword').value;

            // Client-side validation
            if (!email || !password) {
                showError('Please fill in all fields');
                return;
            }

            if (firebaseInitialized && !FirebaseAuth.validateEmail(email)) {
                showError('Please enter a valid email address');
                return;
            }

            showLoading();
            hideError();

            try {
                if (firebaseInitialized) {
                    // Use Firebase authentication
                    const result = await FirebaseAuth.signIn(email, password);

                    if (result.success) {
                        // Session is automatically stored by FirebaseAuth.signIn()
                        console.log('Sign in successful');

                        // Sync with server
                        const idToken = await FirebaseAuth.getIdToken();
                        if (idToken) {
                            await syncUserWithServer(result.user, idToken);
                        }

                        window.location.href = '/index.html';
                    } else {
                        showError(result.error || 'Sign in failed');
                    }
                } else {
                    // Firebase is required for authentication
                    showError('Authentication service is unavailable. Please try again later.');
                    return;
                }
            } catch (error) {
                console.error('Sign in error:', error);
                showError('An error occurred. Please try again.');
            } finally {
                hideLoading();
            }
        });

        // Sign Up functionality
        document.getElementById('signUpBtn').addEventListener('click', async function() {
            const username = document.getElementById('signUpUsername').value.trim();
            const email = document.getElementById('signUpEmail').value.trim();
            const password = document.getElementById('signUpPassword').value;

            // Client-side validation
            if (!username || !email || !password) {
                showError('Please fill in all fields');
                return;
            }

            if (firebaseInitialized) {
                // Use Firebase validation functions
                const emailValidation = FirebaseAuth.validateEmail(email);
                if (!emailValidation) {
                    showError('Please enter a valid email address');
                    return;
                }

                const usernameValidation = FirebaseAuth.validateUsername(username);
                if (!usernameValidation.isValid) {
                    showError(usernameValidation.errors[0]);
                    return;
                }

                const passwordValidation = FirebaseAuth.validatePassword(password);
                if (!passwordValidation.isValid) {
                    showError(passwordValidation.errors[0]);
                    return;
                }
            } else {
                // Basic fallback validation
                if (!email.includes('@') || !email.includes('.')) {
                    showError('Please enter a valid email address');
                    return;
                }

                if (password.length < 6) {
                    showError('Password must be at least 6 characters long');
                    return;
                }

                if (username.length < 2) {
                    showError('Username must be at least 2 characters long');
                    return;
                }
            }

            showLoading();
            hideError();

            try {
                if (firebaseInitialized) {
                    // Use Firebase authentication
                    const result = await FirebaseAuth.signUp(email, password, username);

                    if (result.success) {
                        // Session is automatically stored by FirebaseAuth.signUp()
                        console.log('Sign up successful');

                        // Sync with server
                        const idToken = await FirebaseAuth.getIdToken();
                        if (idToken) {
                            await syncUserWithServer(result.user, idToken);
                        }

                        window.location.href = '/index.html';
                    } else {
                        showError(result.error || 'Sign up failed');
                    }
                } else {
                    // Firebase is required for authentication
                    showError('Authentication service is unavailable. Please try again later.');
                    return;
                }
            } catch (error) {
                console.error('Sign up error:', error);
                showError('An error occurred. Please try again.');
            } finally {
                hideLoading();
            }
        });

        // Sync user with server
        async function syncUserWithServer(user, idToken) {
            try {
                const response = await fetch('/api/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        ...(idToken && { 'Authorization': `Bearer ${idToken}` })
                    },
                    body: JSON.stringify({
                        uid: user.uid,
                        username: user.username,
                        email: user.email
                    })
                });

                if (!response.ok) {
                    console.warn('Failed to sync user with server');
                }
            } catch (error) {
                console.error('Error syncing user with server:', error);
            }
        }
    </script>
</body>
</html>