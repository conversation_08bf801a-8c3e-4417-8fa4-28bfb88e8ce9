# Naroop Development Server Guide

## Running the Server

To run the Naroop development server:

1. Open a terminal/command prompt
2. Navigate to the project directory
3. Execute: `npm run dev`

## Expected Output

```
🔧 Loading Naroop server dependencies...
📦 Basic dependencies loaded
⚙️ Configuration loaded successfully
📝 Logger initialized successfully
🚀 Starting Naroop server...
📍 Environment: development
🌐 Host: localhost
🔌 Port: 3000
✅ Server started successfully!
📱 Your social media platform is ready!
🌐 Open http://localhost:3000 in your browser
```

## Accessing the Application

- Open a web browser and navigate to: `http://localhost:3000`
- Test the health endpoint at: `http://localhost:3000/health`

## Potential Issues

1. **Port Already in Use**
   - Error: `Port 3000 is already in use`
   - Solution: Stop other services using port 3000 or change the PORT environment variable

2. **Firebase Configuration**
   - The server will fall back to client-side authentication if server-side initialization fails
   - No Firebase configuration is required for basic development

## Recommendations

1. Consider updating package.json to use nodemon for auto-restart:
   ```json
   {
     "scripts": {
       "dev": "nodemon server.js"
     }
   }
   ```

2. Create a .env file for custom configuration settings

3. For a better development experience, install optional dependencies:
   ```
   npm install --save-dev compression dotenv express-rate-limit helmet
   ```