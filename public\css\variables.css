/* Naroop CSS Variables */
/* Color Palette */
:root {
    /* Primary Colors */
    --primary-color: #ff6b35;
    --primary-light: #ff8c5a;
    --primary-dark: #e55a2b;
    
    /* Secondary Colors */
    --secondary-color: #ffd23f;
    --secondary-light: #ffdd66;
    --secondary-dark: #e6bd39;
    
    /* Neutral Colors */
    --background-color: #1a1a1a;
    --surface-color: #2d2d2d;
    --card-background: #333333;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #999999;
    --border-color: #444444;
    --divider-color: #555555;
    
    /* Status Colors */
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --error-color: #f44336;
    --info-color: #2196f3;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --gradient-dark: linear-gradient(135deg, #1a1a1a, #2d2d2d);
    --gradient-card: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* Typography */
    --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-heading: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-family-mono: 'Fira Code', 'Monaco', 'Consolas', monospace;
    
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    --font-size-4xl: 2.25rem;
    
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
    
    --line-height-tight: 1.25;
    --line-height-normal: 1.5;
    --line-height-relaxed: 1.75;
    
    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    --border-radius-full: 9999px;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
    
    /* Z-Index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    
    /* Layout */
    --container-max-width: 1200px;
    --sidebar-width: 280px;
    --header-height: 64px;
    
    /* Breakpoints (for reference in media queries) */
    --breakpoint-sm: 640px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 1024px;
    --breakpoint-xl: 1280px;
    --breakpoint-2xl: 1536px;
}

/* Dark theme adjustments */
@media (prefers-color-scheme: dark) {
    :root {
        --background-color: #0a0a0a;
        --surface-color: #1a1a1a;
        --card-background: #2a2a2a;
        --border-color: #333333;
        --divider-color: #444444;
    }
}

/* Light theme override */
[data-theme="light"] {
    --background-color: #ffffff;
    --surface-color: #f8f9fa;
    --card-background: #ffffff;
    --text-primary: #1a1a1a;
    --text-secondary: #4a4a4a;
    --text-muted: #6a6a6a;
    --border-color: #e0e0e0;
    --divider-color: #f0f0f0;
}

/* High contrast theme */
[data-theme="high-contrast"] {
    --background-color: #000000;
    --surface-color: #000000;
    --card-background: #000000;
    --text-primary: #ffffff;
    --text-secondary: #ffffff;
    --text-muted: #cccccc;
    --border-color: #ffffff;
    --divider-color: #ffffff;
    --primary-color: #ffff00;
    --secondary-color: #00ffff;
}
