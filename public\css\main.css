/* Naroop Main Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family-primary);
    font-size: var(--font-size-base);
    line-height: var(--line-height-normal);
    color: var(--text-primary);
    background-color: var(--background-color);
    background-image: var(--gradient-dark);
    min-height: 100vh;
    overflow-x: hidden;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-family-heading);
    font-weight: var(--font-weight-semibold);
    line-height: var(--line-height-tight);
    margin-bottom: var(--spacing-md);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--text-secondary);
}

a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-light);
}

/* Layout Components */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.main-content {
    display: grid;
    grid-template-columns: var(--sidebar-width) 1fr 300px;
    gap: var(--spacing-lg);
    padding: var(--spacing-lg) 0;
    min-height: calc(100vh - var(--header-height));
}

/* Header */
.header {
    background: var(--surface-color);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid var(--border-color);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    height: var(--header-height);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 var(--spacing-lg);
}

.logo {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-bold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Search */
.search-container {
    position: relative;
    flex: 1;
    max-width: 500px;
    margin: 0 var(--spacing-lg);
}

.search-input {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md) var(--spacing-sm) 2.5rem;
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-full);
    color: var(--text-primary);
    font-size: var(--font-size-sm);
    transition: all var(--transition-fast);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
}

.search-icon {
    position: absolute;
    left: var(--spacing-md);
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
}

/* User Actions */
.user-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.preferences-btn,
.notification-btn {
    background: none;
    border: none;
    color: var(--text-secondary);
    font-size: var(--font-size-lg);
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
}

.preferences-btn:hover,
.notification-btn:hover {
    background: var(--card-background);
    color: var(--text-primary);
}

.profile-img {
    width: 40px;
    height: 40px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: var(--font-weight-bold);
    color: white;
    cursor: pointer;
    transition: transform var(--transition-fast);
}

.profile-img:hover {
    transform: scale(1.05);
}

/* Cards */
.card-base {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    backdrop-filter: blur(10px);
}

.card-padding {
    padding: var(--spacing-lg);
}

.card-sticky {
    position: sticky;
    top: calc(var(--header-height) + var(--spacing-lg));
    height: fit-content;
}

/* Buttons */
.btn-base {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    gap: var(--spacing-xs);
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background: var(--surface-color);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover {
    background: var(--card-background);
    border-color: var(--primary-color);
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: var(--font-size-base);
}

/* Utilities */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.focus-ring:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

.hover-lift {
    transition: transform var(--transition-fast);
}

.hover-lift:hover {
    transform: translateY(-2px);
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, var(--card-background) 25%, var(--surface-color) 50%, var(--card-background) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: var(--border-radius-md);
    height: 100px;
    margin-bottom: var(--spacing-md);
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }
    
    .container {
        padding: 0 var(--spacing-sm);
    }
}

@media (max-width: 768px) {
    .header-content {
        padding: 0 var(--spacing-md);
    }
    
    .search-container {
        margin: 0 var(--spacing-sm);
    }
    
    .user-actions {
        gap: var(--spacing-sm);
    }
}
