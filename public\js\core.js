// Core Application Module for Naroop
// Handles main application initialization and coordination

import { authManager } from './authentication.js';

class NaroopCore {
    constructor() {
        this.initialized = false;
        this.modules = {};
        this.eventBus = new EventTarget();
    }

    async init() {
        console.log('🚀 Initializing Naroop Core...');
        
        try {
            // Initialize authentication state
            this.setupAuthenticationHandling();
            
            // Initialize UI components
            this.initializeUI();
            
            // Set up global event handlers
            this.setupGlobalEventHandlers();
            
            // Initialize page-specific functionality
            this.initializePageSpecific();
            
            this.initialized = true;
            console.log('✅ Naroop Core initialized successfully');
            
            // Emit initialization complete event
            this.emit('core:initialized');
            
        } catch (error) {
            console.error('❌ Failed to initialize Naroop Core:', error);
        }
    }

    setupAuthenticationHandling() {
        authManager.onAuthStateChanged((user) => {
            if (user) {
                this.handleUserSignedIn(user);
            } else {
                this.handleUserSignedOut();
            }
        });
    }

    handleUserSignedIn(user) {
        console.log('👤 User signed in:', user.email);
        
        // Update UI for authenticated user
        this.updateUIForAuthenticatedUser(user);
        
        // Emit user signed in event
        this.emit('user:signedIn', { user });
    }

    handleUserSignedOut() {
        console.log('👤 User signed out');
        
        // Update UI for unauthenticated user
        this.updateUIForUnauthenticatedUser();
        
        // Emit user signed out event
        this.emit('user:signedOut');
    }

    updateUIForAuthenticatedUser(user) {
        // Update profile display
        const profileImg = document.querySelector('.profile-img');
        if (profileImg) {
            profileImg.textContent = authManager.getUserInitials();
            profileImg.title = authManager.getUserDisplayName();
        }

        // Show authenticated user elements
        const authElements = document.querySelectorAll('[data-auth="required"]');
        authElements.forEach(el => el.style.display = '');

        // Hide unauthenticated user elements
        const unauthElements = document.querySelectorAll('[data-auth="hidden"]');
        unauthElements.forEach(el => el.style.display = 'none');
    }

    updateUIForUnauthenticatedUser() {
        // Hide authenticated user elements
        const authElements = document.querySelectorAll('[data-auth="required"]');
        authElements.forEach(el => el.style.display = 'none');

        // Show unauthenticated user elements
        const unauthElements = document.querySelectorAll('[data-auth="hidden"]');
        unauthElements.forEach(el => el.style.display = '');

        // Redirect to landing page if on protected page
        if (window.location.pathname === '/app' || window.location.pathname === '/index.html') {
            window.location.href = '/';
        }
    }

    initializeUI() {
        // Initialize loading states
        this.hideLoadingStates();
        
        // Initialize tooltips
        this.initializeTooltips();
        
        // Initialize keyboard navigation
        this.initializeKeyboardNavigation();
    }

    hideLoadingStates() {
        const loadingElements = document.querySelectorAll('.loading-skeleton');
        loadingElements.forEach(el => {
            el.style.display = 'none';
        });
    }

    initializeTooltips() {
        const tooltipElements = document.querySelectorAll('[data-tooltip]');
        tooltipElements.forEach(el => {
            el.addEventListener('mouseenter', this.showTooltip.bind(this));
            el.addEventListener('mouseleave', this.hideTooltip.bind(this));
        });
    }

    showTooltip(event) {
        const element = event.target;
        const tooltipText = element.dataset.tooltip;
        
        if (!tooltipText) return;
        
        const tooltip = document.createElement('div');
        tooltip.className = 'tooltip';
        tooltip.textContent = tooltipText;
        tooltip.style.position = 'absolute';
        tooltip.style.background = 'var(--card-background)';
        tooltip.style.border = '1px solid var(--border-color)';
        tooltip.style.borderRadius = 'var(--border-radius-sm)';
        tooltip.style.padding = 'var(--spacing-xs) var(--spacing-sm)';
        tooltip.style.fontSize = 'var(--font-size-xs)';
        tooltip.style.zIndex = 'var(--z-tooltip)';
        tooltip.style.pointerEvents = 'none';
        
        document.body.appendChild(tooltip);
        
        const rect = element.getBoundingClientRect();
        tooltip.style.left = rect.left + (rect.width / 2) - (tooltip.offsetWidth / 2) + 'px';
        tooltip.style.top = rect.top - tooltip.offsetHeight - 5 + 'px';
        
        element._tooltip = tooltip;
    }

    hideTooltip(event) {
        const element = event.target;
        if (element._tooltip) {
            document.body.removeChild(element._tooltip);
            delete element._tooltip;
        }
    }

    initializeKeyboardNavigation() {
        document.addEventListener('keydown', (e) => {
            // Handle global keyboard shortcuts
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'k':
                        e.preventDefault();
                        this.focusSearch();
                        break;
                    case '/':
                        e.preventDefault();
                        this.focusSearch();
                        break;
                }
            }
        });
    }

    focusSearch() {
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.focus();
        }
    }

    setupGlobalEventHandlers() {
        // Handle clicks on navigation items
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('nav-item')) {
                this.handleNavigationClick(e);
            }
        });
    }

    handleNavigationClick(event) {
        const navItem = event.target;
        const section = navItem.dataset.section;
        
        if (section) {
            this.emit('navigation:change', { section });
        }
    }

    initializePageSpecific() {
        const path = window.location.pathname;
        
        if (path === '/' || path === '/landing.html') {
            this.initializeLandingPage();
        } else if (path === '/app' || path === '/index.html') {
            this.initializeMainApp();
        }
    }

    initializeLandingPage() {
        console.log('🏠 Initializing landing page');
        // Landing page specific initialization
    }

    initializeMainApp() {
        console.log('📱 Initializing main app');
        // Main app specific initialization
    }

    // Event system
    emit(eventName, data = {}) {
        const event = new CustomEvent(eventName, { detail: data });
        this.eventBus.dispatchEvent(event);
    }

    on(eventName, callback) {
        this.eventBus.addEventListener(eventName, callback);
    }

    off(eventName, callback) {
        this.eventBus.removeEventListener(eventName, callback);
    }

    // Module registration
    registerModule(name, module) {
        this.modules[name] = module;
        console.log(`📦 Module registered: ${name}`);
    }

    getModule(name) {
        return this.modules[name];
    }
}

// Create and initialize core instance
const naroopCore = new NaroopCore();

// Auto-initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => naroopCore.init());
} else {
    naroopCore.init();
}

// Export for use in other modules
export { NaroopCore, naroopCore };
export default naroopCore;
