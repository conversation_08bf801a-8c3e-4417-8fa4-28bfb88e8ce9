// Navigation Module for Naroop
// Handles navigation state and routing

import { naroopCore } from './core.js';

class NavigationManager {
    constructor() {
        this.currentSection = 'home';
        this.navigationHistory = [];
        this.init();
    }

    init() {
        this.setupNavigationHandlers();
        this.updateActiveNavigation();
        
        // Listen for navigation changes from core
        naroopCore.on('navigation:change', (event) => {
            this.navigateToSection(event.detail.section);
        });
    }

    setupNavigationHandlers() {
        // Handle navigation item clicks
        document.addEventListener('click', (e) => {
            if (e.target.matches('.nav-item, .nav-item *')) {
                e.preventDefault();
                const navItem = e.target.closest('.nav-item');
                const section = navItem.dataset.section;
                if (section) {
                    this.navigateToSection(section);
                }
            }
        });

        // Handle browser back/forward
        window.addEventListener('popstate', (e) => {
            if (e.state && e.state.section) {
                this.navigateToSection(e.state.section, false);
            }
        });
    }

    navigateToSection(section, updateHistory = true) {
        if (section === this.currentSection) return;

        console.log(`🧭 Navigating to section: ${section}`);

        // Update history
        if (updateHistory) {
            this.navigationHistory.push(this.currentSection);
            history.pushState({ section }, '', `#${section}`);
        }

        // Update current section
        this.currentSection = section;

        // Update UI
        this.updateActiveNavigation();
        this.showSection(section);

        // Emit navigation event
        naroopCore.emit('section:changed', { section, previous: this.navigationHistory[this.navigationHistory.length - 1] });
    }

    updateActiveNavigation() {
        // Remove active class from all nav items
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach(item => {
            item.classList.remove('active');
        });

        // Add active class to current section
        const activeNavItem = document.querySelector(`[data-section="${this.currentSection}"]`);
        if (activeNavItem) {
            activeNavItem.classList.add('active');
        }
    }

    showSection(section) {
        // Hide all sections
        const sections = document.querySelectorAll('.content-section');
        sections.forEach(sec => {
            sec.style.display = 'none';
            sec.classList.remove('active');
        });

        // Show target section
        const targetSection = document.querySelector(`[data-section-content="${section}"]`);
        if (targetSection) {
            targetSection.style.display = 'block';
            targetSection.classList.add('active');
        } else {
            // If no specific section found, show default content
            this.showDefaultContent(section);
        }
    }

    showDefaultContent(section) {
        const mainContent = document.querySelector('.main-content');
        if (!mainContent) return;

        // Create section content based on section name
        let content = '';
        
        switch (section) {
            case 'home':
                content = this.getHomeContent();
                break;
            case 'profile':
                content = this.getProfileContent();
                break;
            case 'messages':
                content = this.getMessagesContent();
                break;
            case 'notifications':
                content = this.getNotificationsContent();
                break;
            case 'settings':
                content = this.getSettingsContent();
                break;
            default:
                content = this.getDefaultContent(section);
        }

        // Update main content area
        const feedSection = mainContent.querySelector('.feed-section');
        if (feedSection) {
            feedSection.innerHTML = content;
        }
    }

    getHomeContent() {
        return `
            <div class="section-header">
                <h2>Home Feed</h2>
                <p>Welcome to your personalized feed of positive stories and community updates.</p>
            </div>
            <div class="posts-container">
                <!-- Posts will be loaded here -->
                <div class="loading-skeleton"></div>
            </div>
        `;
    }

    getProfileContent() {
        return `
            <div class="section-header">
                <h2>Your Profile</h2>
                <p>Manage your profile information and view your posts.</p>
            </div>
            <div class="profile-content">
                <div class="profile-info">
                    <div class="profile-avatar">
                        <div class="profile-img large">U</div>
                    </div>
                    <div class="profile-details">
                        <h3>Your Name</h3>
                        <p>Member since today</p>
                    </div>
                </div>
                <div class="profile-posts">
                    <h4>Your Posts</h4>
                    <p>You haven't posted anything yet. Share your first positive story!</p>
                </div>
            </div>
        `;
    }

    getMessagesContent() {
        return `
            <div class="section-header">
                <h2>Messages</h2>
                <p>Connect with other community members.</p>
            </div>
            <div class="messages-content">
                <div class="messages-placeholder">
                    <h3>No messages yet</h3>
                    <p>Start a conversation with someone in the community!</p>
                </div>
            </div>
        `;
    }

    getNotificationsContent() {
        return `
            <div class="section-header">
                <h2>Notifications</h2>
                <p>Stay updated with community activity.</p>
            </div>
            <div class="notifications-content">
                <div class="notifications-placeholder">
                    <h3>No notifications</h3>
                    <p>You're all caught up!</p>
                </div>
            </div>
        `;
    }

    getSettingsContent() {
        return `
            <div class="section-header">
                <h2>Settings</h2>
                <p>Customize your Naroop experience.</p>
            </div>
            <div class="settings-content">
                <div class="settings-group">
                    <h3>Account Settings</h3>
                    <div class="setting-item">
                        <label>Email Notifications</label>
                        <input type="checkbox" checked>
                    </div>
                    <div class="setting-item">
                        <label>Privacy Mode</label>
                        <input type="checkbox">
                    </div>
                </div>
                <div class="settings-group">
                    <h3>Appearance</h3>
                    <div class="setting-item">
                        <label>Dark Mode</label>
                        <input type="checkbox">
                    </div>
                </div>
            </div>
        `;
    }

    getDefaultContent(section) {
        return `
            <div class="section-header">
                <h2>${section.charAt(0).toUpperCase() + section.slice(1)}</h2>
                <p>This section is coming soon!</p>
            </div>
            <div class="placeholder-content">
                <div class="placeholder-message">
                    <h3>Under Development</h3>
                    <p>We're working hard to bring you this feature. Stay tuned!</p>
                </div>
            </div>
        `;
    }

    // Public methods
    getCurrentSection() {
        return this.currentSection;
    }

    goBack() {
        if (this.navigationHistory.length > 0) {
            const previousSection = this.navigationHistory.pop();
            this.navigateToSection(previousSection, false);
            history.back();
        }
    }

    canGoBack() {
        return this.navigationHistory.length > 0;
    }
}

// Create and export navigation manager instance
const navigationManager = new NavigationManager();

// Register with core
naroopCore.registerModule('navigation', navigationManager);

// Export for use in other modules
export { NavigationManager, navigationManager };
export default navigationManager;
