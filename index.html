<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Naroop - Connect & Share</title>

    <!-- External CSS Files -->
    <link rel="stylesheet" href="./public/css/variables.css?v=3">
    <link rel="stylesheet" href="./public/css/main.css?v=3">
    <link rel="stylesheet" href="./public/css/modern-animations.css?v=1">
    <link rel="stylesheet" href="./public/css/preferences.css?v=2">
    <link rel="stylesheet" href="./public/css/responsive.css?v=2">
    <link rel="stylesheet" href="./public/css/posts.css?v=2">
    <link rel="stylesheet" href="./public/css/forms.css?v=2">
    <link rel="stylesheet" href="./public/css/notifications.css?v=2">
    <link rel="stylesheet" href="./public/css/performance.css?v=2">
    <link rel="stylesheet" href="./public/css/mobile-performance.css?v=2">
    <link rel="stylesheet" href="./public/css/scroll-sections.css?v=2">
    <link rel="stylesheet" href="./public/css/index-specific.css?v=1">
</head>
<body>
    <!-- Skip to content link for accessibility -->
    <a href="#main-content" class="skip-link">Skip to main content</a>

    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">Naroop</div>
                <div class="search-container">
                    <div class="search-icon" aria-hidden="true">🔍</div>
                    <label for="search-input" class="sr-only">Search communities, topics, and people</label>
                    <input type="text" id="search-input" class="search-input"
                           placeholder="Search communities, topics, people..."
                           aria-label="Search communities, topics, and people">
                </div>
                <div class="user-actions">
                    <button class="preferences-btn" id="preferencesBtn" aria-label="Open accessibility preferences">
                        <span aria-hidden="true">⚙️</span>
                    </button>
                    <button class="notification-btn" aria-label="View notifications">
                        <span aria-hidden="true">🔔</span>
                    </button>
                    <div class="profile-img" role="button" tabindex="0" aria-label="User profile menu">N</div>
                </div>
            </div>
        </div>
    </header>

    <div class="container">
        <div class="main-content" id="main-content">
            <aside class="sidebar card-base card-padding card-sticky slide-in-left">
                <h3>Navigation</h3>
                <nav role="navigation" aria-label="Main navigation" class="stagger-children">
                    <div class="nav-item active focus-ring" data-section="feed" aria-current="page" role="button" tabindex="0" aria-label="Feed - Current page">
                        <div class="nav-icon" aria-hidden="true">📱</div>
                        <span>Feed</span>
                    </div>
                    <div class="nav-item focus-ring" data-section="explore" role="button" tabindex="0" aria-label="Explore content">
                        <div class="nav-icon" aria-hidden="true">🌟</div>
                        <span>Explore</span>
                    </div>
                    <div class="nav-item focus-ring" data-section="messages" role="button" tabindex="0" aria-label="Messages">
                        <div class="nav-icon" aria-hidden="true">💬</div>
                        <span>Messages</span>
                    </div>
                    <div class="nav-item focus-ring" data-section="profile" role="button" tabindex="0" aria-label="Profile">
                        <div class="nav-icon" aria-hidden="true">👤</div>
                        <span>Profile</span>
                    </div>
                    <div class="nav-item focus-ring" data-section="communities" role="button" tabindex="0" aria-label="Communities">
                        <div class="nav-icon" aria-hidden="true">🎯</div>
                        <span>Communities</span>
                    </div>
                    <div class="nav-item focus-ring" data-section="analytics" role="button" tabindex="0" aria-label="Analytics">
                        <div class="nav-icon" aria-hidden="true">📊</div>
                        <span>Analytics</span>
                    </div>
                </nav>
            </aside>

            <!-- Feed Section -->
            <main class="feed content-section active" id="feed-section">
                <div class="feed-header">
                    <h2 class="feed-title">Your Feed</h2>
                    <div class="story-prompt">
                        <h4><span aria-hidden="true">✨</span> Share Your Story</h4>
                        <p>What positive experience would you like to share with the community today?</p>
                        <button class="create-post-btn btn-base btn-primary btn-lg hover-lift focus-ring" id="createPostBtn" onclick="addBounceEffect(this)">Create Post</button>
                    </div>
                </div>
                <div class="feed-content">
                    <!-- Loading State (hidden by default) -->
                    <div class="loading-state" id="feedLoading" style="display: none;">
                        <div class="loading-skeleton card"></div>
                        <div class="loading-skeleton card"></div>
                        <div class="loading-skeleton card"></div>
                    </div>

                    <!-- Empty State -->
                    <div class="empty-state" id="feedEmpty">
                        <h4>Welcome to your feed!</h4>
                        <p>Start by creating your first post or connecting with other community members. Your positive stories and experiences will appear here.</p>
                    </div>

                    <!-- Posts Container -->
                    <div id="postsContainer">
                        <!-- Posts will be loaded here -->
                    </div>

                    <div class="load-more">Load More Posts</div>
                </div>
            </main>

            <!-- Explore Section -->
            <section class="feed content-section" id="explore-section">
                <div class="feed-header">
                    <h2 class="feed-title">Explore</h2>
                </div>
                <div class="feed-content">
                    <div class="explore-content">
                        <h3><span aria-hidden="true">🔍</span> Discover New Content</h3>
                        <p>Explore trending posts and discover new voices in the community.</p>
                        <div class="explore-categories">
                            <div class="category-tag">#Trending</div>
                            <div class="category-tag">#BlackExcellence</div>
                            <div class="category-tag">#CommunityLove</div>
                            <div class="category-tag">#Inspiration</div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Trending Topics Section -->
            <section class="feed content-section" id="trending-section">
                <div class="feed-header">
                    <h2 class="feed-title">Trending Topics</h2>
                </div>
                <div class="feed-content">
                    <div class="trending-topic-detail" id="trending-detail-container">
                        <div class="empty-state">
                            <div class="empty-state-icon" aria-hidden="true">📈</div>
                            <h3>No trending topics yet</h3>
                            <p>Trending topics will appear here as the community grows and creates engaging content.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Messages Section -->
            <section class="feed content-section" id="messages-section">
                <div class="feed-header">
                    <h2 class="feed-title">Messages</h2>
                </div>
                <div class="feed-content">
                    <div class="messages-content">
                        <h3><span aria-hidden="true">💬</span> Your Messages</h3>
                        <p>Connect and communicate with your community.</p>
                        <div class="messages-placeholder">
                            <p>No messages yet. Start a conversation!</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Profile Section -->
            <section class="feed content-section" id="profile-section">
                <div class="feed-header">
                    <h2 class="feed-title">Profile</h2>
                </div>
                <div class="feed-content">
                    <div class="profile-content">
                        <div class="profile-header">
                            <div class="profile-avatar">👤</div>
                            <div class="profile-info">
                                <h3 id="profileUsername">Loading...</h3>
                                <p id="profileEmail">Loading...</p>
                            </div>
                        </div>
                        <div class="profile-stats">
                            <div class="stat">
                                <span class="stat-number">0</span>
                                <span class="stat-label">Posts</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">0</span>
                                <span class="stat-label">Followers</span>
                            </div>
                            <div class="stat">
                                <span class="stat-number">0</span>
                                <span class="stat-label">Following</span>
                            </div>
                        </div>
                        <div class="profile-actions">
                            <button class="nav-btn" id="signOutBtn">Sign Out</button>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Communities Section -->
            <section class="feed content-section" id="communities-section">
                <div class="feed-header">
                    <h2 class="feed-title">Communities</h2>
                </div>
                <div class="feed-content">
                    <div class="communities-content">
                        <h3><span aria-hidden="true">🎯</span> Join Communities</h3>
                        <p>Connect with like-minded people and join communities that inspire you.</p>
                        <div class="communities-grid" id="communities-container">
                            <div class="empty-state">
                                <div class="empty-state-icon" aria-hidden="true">👥</div>
                                <h4>No communities yet</h4>
                                <p>Communities will be created as the platform grows. Be the first to start meaningful conversations!</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Analytics Section -->
            <section class="feed content-section" id="analytics-section">
                <div class="feed-header">
                    <h2 class="feed-title">Analytics</h2>
                </div>
                <div class="feed-content">
                    <div class="analytics-content">
                        <h3><span aria-hidden="true">📊</span> Your Analytics</h3>
                        <p>Track your engagement and community impact.</p>
                        <div class="analytics-grid" id="analytics-container">
                            <div class="empty-state">
                                <div class="empty-state-icon" aria-hidden="true">📊</div>
                                <h4>No analytics data yet</h4>
                                <p>Start posting and engaging with the community to see your analytics here.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <aside class="trending card-base card-padding card-sticky slide-in-right">
                <h3>Trending Topics</h3>
                <div id="trending-container" class="fade-in-up">
                    <div class="empty-state">
                        <div class="empty-state-icon" aria-hidden="true">📈</div>
                        <h4>No trending topics yet</h4>
                        <p>Trending topics will appear here as the community grows and engages with content.</p>
                    </div>
                </div>
            </aside>
        </div>
    </div>

    <!-- Mobile Navigation Bar -->
    <nav class="mobile-nav" role="navigation" aria-label="Mobile navigation">
        <a href="#" class="mobile-nav-item active" data-section="feed" aria-label="Feed">
            <div class="mobile-nav-icon">📱</div>
            <span>Feed</span>
        </a>
        <a href="#" class="mobile-nav-item" data-section="explore" aria-label="Explore">
            <div class="mobile-nav-icon">🌟</div>
            <span>Explore</span>
        </a>
        <a href="#" class="mobile-nav-item" data-section="messages" aria-label="Messages">
            <div class="mobile-nav-icon">💬</div>
            <span>Messages</span>
        </a>
        <a href="#" class="mobile-nav-item" data-section="profile" aria-label="Profile">
            <div class="mobile-nav-icon">👤</div>
            <span>Profile</span>
        </a>
    </nav>

    <!-- Accessibility Preferences Panel -->
    <div class="preferences-panel" id="preferencesPanel" role="dialog" aria-labelledby="preferences-title" aria-hidden="true">
        <h3 id="preferences-title">Accessibility Preferences</h3>

        <div class="preference-group">
            <h4>Visual Preferences</h4>
            <div class="toggle-switch">
                <label for="high-contrast">High Contrast Mode</label>
                <input type="checkbox" id="high-contrast" aria-describedby="high-contrast-desc">
            </div>
            <p id="high-contrast-desc" class="preference-description">Increases contrast for better visibility</p>

            <div class="toggle-switch">
                <label for="large-text">Large Text</label>
                <input type="checkbox" id="large-text" aria-describedby="large-text-desc">
            </div>
            <p id="large-text-desc" class="preference-description">Increases font size for better readability</p>
        </div>

        <div class="preference-group">
            <h4>Motion Preferences</h4>
            <div class="toggle-switch">
                <label for="reduce-motion">Reduce Motion</label>
                <input type="checkbox" id="reduce-motion" aria-describedby="reduce-motion-desc">
            </div>
            <p id="reduce-motion-desc" class="preference-description">Minimizes animations and transitions</p>
        </div>

        <div class="preference-group">
            <h4>Keyboard Navigation</h4>
            <div class="toggle-switch">
                <label for="enhanced-focus">Enhanced Focus Indicators</label>
                <input type="checkbox" id="enhanced-focus" aria-describedby="enhanced-focus-desc">
            </div>
            <p id="enhanced-focus-desc" class="preference-description">Makes focus indicators more visible</p>
        </div>

        <button class="btn-base btn-primary btn-md" onclick="closePreferences()" style="width: 100%; margin-top: var(--spacing-lg);">
            Close Preferences
        </button>
    </div>











    <!-- Include essential JavaScript files for functionality -->
    <script type="module" src="./public/js/firebase-config.js"></script>
    <script type="module" src="./public/js/authentication.js"></script>
    <script type="module" src="./public/js/core.js"></script>
    <script type="module" src="./public/js/navigation.js"></script>
    <script type="module" src="./public/js/posts.js"></script>
    <script type="module" src="./public/js/profile.js"></script>
    <script src="./public/js/modal-system.js"></script>
    <script src="./public/js/accessibility-preferences.js"></script>

    <script>
    // Notification button handler
    document.addEventListener('DOMContentLoaded', function() {
      var notificationBtn = document.querySelector('.notification-btn');
      if (notificationBtn) {
        notificationBtn.addEventListener('click', function() {
          if (window.ModalSystem && window.ModalSystem.open) {
            window.ModalSystem.open('<h2>Notifications</h2><p>No new notifications yet.</p>');
          } else if (window.ErrorHandler) {
            window.ErrorHandler.showNotification('No new notifications yet.', 'info');
          } else {
            console.log('No new notifications yet.');
          }
        });
      }

      // Note: Community and post interaction handlers are now managed by the Posts module
    });

    // Micro-interactions and UI enhancements
    function addBounceEffect(element) {
        if (!document.body.classList.contains('reduce-motion-mode')) {
            element.classList.add('bounce-on-click');
            setTimeout(() => {
                element.classList.remove('bounce-on-click');
            }, 600);
        }
    }

    // Show loading state
    function showLoadingState() {
        const loading = document.getElementById('feedLoading');
        const empty = document.getElementById('feedEmpty');
        const container = document.getElementById('postsContainer');

        if (loading) loading.style.display = 'block';
        if (empty) empty.style.display = 'none';
        if (container) container.style.display = 'none';
    }

    // Hide loading state
    function hideLoadingState() {
        const loading = document.getElementById('feedLoading');
        if (loading) loading.style.display = 'none';
    }

    // Show empty state
    function showEmptyState() {
        const empty = document.getElementById('feedEmpty');
        const container = document.getElementById('postsContainer');

        if (empty) empty.style.display = 'block';
        if (container) container.style.display = 'none';
    }

    // Show posts container
    function showPostsContainer() {
        const empty = document.getElementById('feedEmpty');
        const container = document.getElementById('postsContainer');

        if (empty) empty.style.display = 'none';
        if (container) {
            container.style.display = 'block';
            container.classList.add('fade-in-up');
        }
    }

    // Add notification pulse effect
    function addNotificationPulse() {
        const notificationBtn = document.querySelector('.notification-btn');
        if (notificationBtn && !document.body.classList.contains('reduce-motion-mode')) {
            notificationBtn.classList.add('notification-pulse');
            setTimeout(() => {
                notificationBtn.classList.remove('notification-pulse');
            }, 2000);
        }
    }

    // Enhanced keyboard navigation
    document.addEventListener('keydown', function(e) {
        // Handle Enter key on elements with tabindex
        if (e.key === 'Enter') {
            const target = e.target;
            if (target.hasAttribute('tabindex') && target.getAttribute('role') === 'button') {
                target.click();
            }
        }

        // Handle arrow key navigation in sidebar
        if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
            const navItems = document.querySelectorAll('.nav-item[tabindex="0"]');
            const currentIndex = Array.from(navItems).indexOf(document.activeElement);

            if (currentIndex !== -1) {
                e.preventDefault();
                let nextIndex;

                if (e.key === 'ArrowDown') {
                    nextIndex = (currentIndex + 1) % navItems.length;
                } else {
                    nextIndex = (currentIndex - 1 + navItems.length) % navItems.length;
                }

                navItems[nextIndex].focus();
            }
        }
    });

    // Enhanced UI interactions and animations
    document.addEventListener('DOMContentLoaded', function() {
        // Add fade-in animation to main content
        const mainContent = document.querySelector('.main-content');
        if (mainContent && !document.body.classList.contains('reduce-motion-mode')) {
            mainContent.classList.add('fade-in-up');
        }

        // Initialize mobile navigation
        initializeMobileNavigation();

        // Initialize enhanced interactions
        initializeEnhancedInteractions();

        // Simulate loading state for demonstration
        setTimeout(() => {
            hideLoadingState();
            showEmptyState();
        }, 1000);
    });

    // Mobile navigation functionality
    function initializeMobileNavigation() {
        const mobileNavItems = document.querySelectorAll('.mobile-nav-item');
        const desktopNavItems = document.querySelectorAll('.nav-item');

        mobileNavItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const section = this.dataset.section;

                // Update mobile nav active state
                mobileNavItems.forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');

                // Update desktop nav active state
                desktopNavItems.forEach(nav => nav.classList.remove('active'));
                const correspondingDesktopNav = document.querySelector(`.nav-item[data-section="${section}"]`);
                if (correspondingDesktopNav) {
                    correspondingDesktopNav.classList.add('active');
                }

                // Trigger section change
                if (window.NavigationManager && window.NavigationManager.showSection) {
                    window.NavigationManager.showSection(section);
                }
            });
        });
    }

    // Enhanced interactions
    function initializeEnhancedInteractions() {
        // Add ripple effect to buttons
        const buttons = document.querySelectorAll('.btn-base');
        buttons.forEach(button => {
            button.addEventListener('click', createRippleEffect);
        });

        // Add hover sound feedback (optional)
        const interactiveElements = document.querySelectorAll('.nav-item, .btn-base, .card-base');
        interactiveElements.forEach(element => {
            element.addEventListener('mouseenter', function() {
                if (!document.body.classList.contains('reduce-motion-mode')) {
                    this.style.transform = 'translateY(-1px)';
                }
            });

            element.addEventListener('mouseleave', function() {
                this.style.transform = '';
            });
        });

        // Intersection Observer for scroll animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('fade-in-up');
                }
            });
        }, observerOptions);

        // Observe cards and sections
        const observeElements = document.querySelectorAll('.card-base, .content-section');
        observeElements.forEach(el => observer.observe(el));
    }

    // Ripple effect for buttons
    function createRippleEffect(e) {
        if (document.body.classList.contains('reduce-motion-mode')) return;

        const button = e.currentTarget;
        const ripple = document.createElement('span');
        const rect = button.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = e.clientX - rect.left - size / 2;
        const y = e.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');

        button.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }
    </script>

    <!-- Generic Modal Structure -->
    <div id="action-modal" class="modal-overlay" aria-hidden="true">
        <div class="modal-content" role="dialog" aria-modal="true">
            <div id="modal-body-content">
                <!-- Dynamic content will be loaded here by JavaScript -->
            </div>
            <button class="modal-close-button" aria-label="Close modal">&times;</button>
        </div>
    </div>
</body>
</html>
