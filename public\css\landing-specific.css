/* Naroop Landing Page (landing.html) Specific Styles */

/* Hero section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-dark);
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    z-index: 1;
}

.hero-content {
    text-align: center;
    max-width: 800px;
    padding: var(--spacing-xl);
    z-index: 2;
    position: relative;
}

.hero-title {
    font-size: clamp(2.5rem, 8vw, 5rem);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-lg);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    line-height: var(--line-height-tight);
}

.hero-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xl);
    line-height: var(--line-height-relaxed);
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.hero-buttons {
    display: flex;
    gap: var(--spacing-lg);
    justify-content: center;
    flex-wrap: wrap;
}

.hero-btn {
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    text-decoration: none;
    transition: all var(--transition-normal);
    min-width: 160px;
    text-align: center;
    cursor: pointer;
    border: none;
}

.hero-btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-lg);
}

.hero-btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

.hero-btn-secondary {
    background: transparent;
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.hero-btn-secondary:hover {
    background: var(--surface-color);
    border-color: var(--primary-color);
}

/* Features section */
.features {
    padding: var(--spacing-xxl) 0;
    background: var(--surface-color);
}

.features-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.features-header {
    text-align: center;
    margin-bottom: var(--spacing-xxl);
}

.features-title {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-lg);
}

.features-subtitle {
    font-size: var(--font-size-xl);
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.feature-card {
    background: var(--card-background);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    text-align: center;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.1), transparent);
    transition: left var(--transition-slow);
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: var(--spacing-lg);
    color: var(--primary-color);
}

.feature-title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-semibold);
    margin-bottom: var(--spacing-md);
}

.feature-description {
    color: var(--text-secondary);
    line-height: var(--line-height-relaxed);
}

/* CTA section */
.cta {
    padding: var(--spacing-xxl) 0;
    background: var(--gradient-primary);
    color: white;
    text-align: center;
}

.cta-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
}

.cta-title {
    font-size: var(--font-size-4xl);
    font-weight: var(--font-weight-bold);
    margin-bottom: var(--spacing-lg);
}

.cta-subtitle {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-xl);
    opacity: 0.9;
}

.cta-button {
    background: white;
    color: var(--primary-color);
    padding: var(--spacing-md) var(--spacing-xl);
    border-radius: var(--border-radius-full);
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    text-decoration: none;
    transition: all var(--transition-normal);
    display: inline-block;
    box-shadow: var(--shadow-lg);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    background: var(--surface-color);
}

/* Authentication modal styles */
.auth-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    backdrop-filter: blur(5px);
}

.auth-modal-content {
    background: var(--card-background);
    border-radius: var(--border-radius-lg);
    width: 90%;
    max-width: 400px;
    max-height: 90vh;
    overflow-y: auto;
    position: relative;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.auth-modal-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-color);
    text-align: center;
}

.auth-modal-title {
    font-size: var(--font-size-2xl);
    font-weight: var(--font-weight-semibold);
    margin: 0;
}

.auth-modal-body {
    padding: var(--spacing-lg);
}

.auth-close {
    position: absolute;
    top: var(--spacing-md);
    right: var(--spacing-md);
    background: none;
    border: none;
    font-size: var(--font-size-2xl);
    color: var(--text-muted);
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--border-radius-sm);
    transition: all var(--transition-fast);
}

.auth-close:hover {
    background: var(--surface-color);
    color: var(--text-primary);
}

/* Responsive design for landing page */
@media (max-width: 768px) {
    .hero-title {
        font-size: var(--font-size-4xl);
    }

    .hero-subtitle {
        font-size: var(--font-size-lg);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-btn {
        width: 100%;
        max-width: 280px;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-lg);
    }

    .features-title,
    .cta-title {
        font-size: var(--font-size-3xl);
    }

    .auth-modal-content {
        width: 95%;
        margin: var(--spacing-md);
    }
}