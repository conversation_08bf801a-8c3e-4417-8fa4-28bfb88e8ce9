// Authentication Module for Naroop
// Handles user authentication flows and state management

import { firebaseAuth } from './firebase-config.js';

class AuthenticationManager {
    constructor() {
        this.currentUser = null;
        this.authStateListeners = [];
        this.init();
    }

    async init() {
        // Wait for Firebase to initialize
        if (firebaseAuth.initialized) {
            this.setupAuthStateListener();
        } else {
            // Wait for Firebase initialization
            setTimeout(() => this.init(), 100);
        }
    }

    setupAuthStateListener() {
        firebaseAuth.auth.onAuthStateChanged((user) => {
            this.currentUser = user;
            this.notifyAuthStateListeners(user);
        });
    }

    // Add auth state listener
    onAuthStateChanged(callback) {
        this.authStateListeners.push(callback);
        // Call immediately with current state
        if (this.currentUser !== null) {
            callback(this.currentUser);
        }
    }

    // Notify all listeners of auth state change
    notifyAuthStateListeners(user) {
        this.authStateListeners.forEach(callback => {
            try {
                callback(user);
            } catch (error) {
                console.error('Error in auth state listener:', error);
            }
        });
    }

    // Sign in with email and password
    async signIn(email, password) {
        try {
            const userCredential = await firebaseAuth.signInWithEmailAndPassword(email, password);
            return { success: true, user: userCredential.user };
        } catch (error) {
            console.error('Sign in error:', error);
            return { success: false, error: error.message };
        }
    }

    // Sign up with email and password
    async signUp(email, password) {
        try {
            const userCredential = await firebaseAuth.createUserWithEmailAndPassword(email, password);
            return { success: true, user: userCredential.user };
        } catch (error) {
            console.error('Sign up error:', error);
            return { success: false, error: error.message };
        }
    }

    // Sign out
    async signOut() {
        try {
            await firebaseAuth.signOut();
            return { success: true };
        } catch (error) {
            console.error('Sign out error:', error);
            return { success: false, error: error.message };
        }
    }

    // Get current user
    getCurrentUser() {
        return this.currentUser;
    }

    // Check if user is authenticated
    isAuthenticated() {
        return this.currentUser !== null;
    }

    // Get user display name
    getUserDisplayName() {
        if (!this.currentUser) return 'Guest';
        return this.currentUser.displayName || this.currentUser.email || 'User';
    }

    // Get user initials for avatar
    getUserInitials() {
        const displayName = this.getUserDisplayName();
        if (displayName === 'Guest') return 'G';
        
        const names = displayName.split(' ');
        if (names.length >= 2) {
            return (names[0][0] + names[1][0]).toUpperCase();
        }
        return displayName[0].toUpperCase();
    }
}

// Create and export authentication manager instance
const authManager = new AuthenticationManager();

// Export for use in other modules
export { AuthenticationManager, authManager };
export default authManager;
