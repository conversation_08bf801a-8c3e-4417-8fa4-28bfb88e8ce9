// Profile Module for Naroop
// Handles user profile management and display

import { naroopCore } from './core.js';
import { authManager } from './authentication.js';

class ProfileManager {
    constructor() {
        this.userProfile = null;
        this.isEditing = false;
        this.init();
    }

    init() {
        this.setupEventHandlers();
        this.loadUserProfile();
        
        // Register with core
        naroopCore.registerModule('profile', this);
        
        // Listen for auth state changes
        authManager.onAuthStateChanged((user) => {
            if (user) {
                this.loadUserProfile();
            } else {
                this.clearProfile();
            }
        });
    }

    setupEventHandlers() {
        document.addEventListener('click', (e) => {
            if (e.target.matches('.edit-profile-btn, .edit-profile-btn *')) {
                e.preventDefault();
                this.toggleEditMode();
            }
            
            if (e.target.matches('.save-profile-btn, .save-profile-btn *')) {
                e.preventDefault();
                this.saveProfile();
            }
            
            if (e.target.matches('.cancel-edit-btn, .cancel-edit-btn *')) {
                e.preventDefault();
                this.cancelEdit();
            }
            
            if (e.target.matches('.profile-img, .profile-img *')) {
                e.preventDefault();
                this.showProfileModal();
            }
        });

        // Handle profile form submission
        document.addEventListener('submit', (e) => {
            if (e.target.matches('.profile-form')) {
                e.preventDefault();
                this.handleProfileUpdate(e);
            }
        });
    }

    async loadUserProfile() {
        const currentUser = authManager.getCurrentUser();
        if (!currentUser) {
            this.clearProfile();
            return;
        }

        try {
            // In a real app, this would fetch from the server
            const response = await fetch(`/api/users/${currentUser.uid}/profile`);
            
            if (response.ok) {
                this.userProfile = await response.json();
            } else {
                // Create default profile for new users
                this.userProfile = {
                    uid: currentUser.uid,
                    email: currentUser.email,
                    displayName: currentUser.displayName || currentUser.email.split('@')[0],
                    bio: '',
                    location: '',
                    website: '',
                    joinedAt: new Date().toISOString(),
                    postsCount: 0,
                    followersCount: 0,
                    followingCount: 0
                };
            }
            
            this.updateProfileDisplay();
            
        } catch (error) {
            console.error('Error loading user profile:', error);
            this.showErrorState();
        }
    }

    updateProfileDisplay() {
        if (!this.userProfile) return;

        // Update profile image/initials
        const profileImgs = document.querySelectorAll('.profile-img');
        const initials = this.getUserInitials(this.userProfile.displayName);
        
        profileImgs.forEach(img => {
            img.textContent = initials;
            img.title = this.userProfile.displayName;
        });

        // Update profile details in profile section
        this.updateProfileSection();
    }

    updateProfileSection() {
        const profileSection = document.querySelector('[data-section-content="profile"]');
        if (!profileSection) return;

        const joinedDate = new Date(this.userProfile.joinedAt).toLocaleDateString();
        
        profileSection.innerHTML = `
            <div class="profile-header">
                <div class="profile-cover">
                    <div class="profile-avatar-large">
                        <div class="profile-img large">${this.getUserInitials(this.userProfile.displayName)}</div>
                        <button class="change-avatar-btn" title="Change avatar">📷</button>
                    </div>
                </div>
                <div class="profile-info">
                    <div class="profile-details">
                        <h2 class="profile-name">${this.userProfile.displayName}</h2>
                        <p class="profile-email">${this.userProfile.email}</p>
                        <p class="profile-joined">Member since ${joinedDate}</p>
                        ${this.userProfile.bio ? `<p class="profile-bio">${this.userProfile.bio}</p>` : ''}
                        ${this.userProfile.location ? `<p class="profile-location">📍 ${this.userProfile.location}</p>` : ''}
                        ${this.userProfile.website ? `<p class="profile-website">🔗 <a href="${this.userProfile.website}" target="_blank">${this.userProfile.website}</a></p>` : ''}
                    </div>
                    <div class="profile-stats">
                        <div class="stat">
                            <span class="stat-number">${this.userProfile.postsCount || 0}</span>
                            <span class="stat-label">Posts</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">${this.userProfile.followersCount || 0}</span>
                            <span class="stat-label">Followers</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">${this.userProfile.followingCount || 0}</span>
                            <span class="stat-label">Following</span>
                        </div>
                    </div>
                    <div class="profile-actions">
                        <button class="btn-base btn-primary edit-profile-btn">
                            Edit Profile
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="profile-content">
                <div class="profile-tabs">
                    <button class="tab-btn active" data-tab="posts">Posts</button>
                    <button class="tab-btn" data-tab="likes">Likes</button>
                    <button class="tab-btn" data-tab="media">Media</button>
                </div>
                
                <div class="tab-content active" data-tab-content="posts">
                    <div class="user-posts">
                        ${this.userProfile.postsCount > 0 ? 
                            '<div class="loading-skeleton"></div>' : 
                            '<div class="empty-posts"><p>You haven\'t posted anything yet. Share your first story!</p></div>'
                        }
                    </div>
                </div>
                
                <div class="tab-content" data-tab-content="likes">
                    <div class="empty-likes">
                        <p>Posts you like will appear here.</p>
                    </div>
                </div>
                
                <div class="tab-content" data-tab-content="media">
                    <div class="empty-media">
                        <p>Photos and videos you've shared will appear here.</p>
                    </div>
                </div>
            </div>
        `;

        // Set up tab switching
        this.setupProfileTabs();
    }

    setupProfileTabs() {
        const tabBtns = document.querySelectorAll('.tab-btn');
        const tabContents = document.querySelectorAll('.tab-content');

        tabBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const tabName = btn.dataset.tab;
                
                // Remove active class from all tabs
                tabBtns.forEach(b => b.classList.remove('active'));
                tabContents.forEach(c => c.classList.remove('active'));
                
                // Add active class to clicked tab
                btn.classList.add('active');
                const targetContent = document.querySelector(`[data-tab-content="${tabName}"]`);
                if (targetContent) {
                    targetContent.classList.add('active');
                }
            });
        });
    }

    toggleEditMode() {
        if (this.isEditing) {
            this.cancelEdit();
        } else {
            this.enterEditMode();
        }
    }

    enterEditMode() {
        this.isEditing = true;
        
        const profileDetails = document.querySelector('.profile-details');
        if (!profileDetails) return;

        profileDetails.innerHTML = `
            <form class="profile-form">
                <div class="form-group">
                    <label for="displayName">Display Name</label>
                    <input type="text" id="displayName" name="displayName" 
                           value="${this.userProfile.displayName}" 
                           class="form-input" required>
                </div>
                
                <div class="form-group">
                    <label for="bio">Bio</label>
                    <textarea id="bio" name="bio" 
                              class="form-input" 
                              rows="3" 
                              maxlength="160"
                              placeholder="Tell us about yourself...">${this.userProfile.bio || ''}</textarea>
                    <small class="form-help">Max 160 characters</small>
                </div>
                
                <div class="form-group">
                    <label for="location">Location</label>
                    <input type="text" id="location" name="location" 
                           value="${this.userProfile.location || ''}" 
                           class="form-input" 
                           placeholder="City, Country">
                </div>
                
                <div class="form-group">
                    <label for="website">Website</label>
                    <input type="url" id="website" name="website" 
                           value="${this.userProfile.website || ''}" 
                           class="form-input" 
                           placeholder="https://yourwebsite.com">
                </div>
                
                <div class="form-buttons">
                    <button type="button" class="btn-base btn-secondary cancel-edit-btn">
                        Cancel
                    </button>
                    <button type="submit" class="btn-base btn-primary save-profile-btn">
                        Save Changes
                    </button>
                </div>
            </form>
        `;
    }

    cancelEdit() {
        this.isEditing = false;
        this.updateProfileDisplay();
    }

    async handleProfileUpdate(event) {
        const form = event.target;
        const formData = new FormData(form);
        
        const updatedProfile = {
            ...this.userProfile,
            displayName: formData.get('displayName'),
            bio: formData.get('bio'),
            location: formData.get('location'),
            website: formData.get('website')
        };

        try {
            // In a real app, this would send to the server
            const response = await fetch(`/api/users/${this.userProfile.uid}/profile`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(updatedProfile)
            });

            if (response.ok) {
                this.userProfile = updatedProfile;
                this.isEditing = false;
                this.updateProfileDisplay();
                this.showSuccessMessage('Profile updated successfully!');
            } else {
                throw new Error('Failed to update profile');
            }
            
        } catch (error) {
            console.error('Error updating profile:', error);
            this.showErrorMessage('Failed to update profile. Please try again.');
        }
    }

    showProfileModal() {
        const modalHTML = `
            <div class="modal-overlay">
                <div class="modal-content profile-modal">
                    <div class="modal-header">
                        <h2>${this.userProfile?.displayName || 'Profile'}</h2>
                        <button class="modal-close-button">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="profile-modal-content">
                            <div class="profile-avatar-display">
                                <div class="profile-img extra-large">
                                    ${this.getUserInitials(this.userProfile?.displayName)}
                                </div>
                            </div>
                            <div class="profile-quick-info">
                                <h3>${this.userProfile?.displayName || 'User'}</h3>
                                <p>${this.userProfile?.email || ''}</p>
                                ${this.userProfile?.bio ? `<p class="bio">${this.userProfile.bio}</p>` : ''}
                            </div>
                            <div class="profile-modal-actions">
                                <button class="btn-base btn-primary edit-profile-btn">
                                    Edit Profile
                                </button>
                                <button class="btn-base btn-secondary modal-close">
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        if (window.modalSystem) {
            window.modalSystem.displayModal(modalHTML);
        }
    }

    clearProfile() {
        this.userProfile = null;
        this.isEditing = false;
        
        // Clear profile displays
        const profileImgs = document.querySelectorAll('.profile-img');
        profileImgs.forEach(img => {
            img.textContent = 'G';
            img.title = 'Guest';
        });
    }

    showErrorState() {
        const profileSection = document.querySelector('[data-section-content="profile"]');
        if (profileSection) {
            profileSection.innerHTML = `
                <div class="error-state">
                    <h3>Unable to load profile</h3>
                    <p>Please try again later.</p>
                    <button class="btn-base btn-primary" onclick="location.reload()">
                        Retry
                    </button>
                </div>
            `;
        }
    }

    showSuccessMessage(message) {
        // Simple success notification
        const notification = document.createElement('div');
        notification.className = 'notification success';
        notification.textContent = message;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    showErrorMessage(message) {
        // Simple error notification
        const notification = document.createElement('div');
        notification.className = 'notification error';
        notification.textContent = message;
        notification.style.position = 'fixed';
        notification.style.top = '20px';
        notification.style.right = '20px';
        notification.style.zIndex = '9999';
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    // Utility methods
    getUserInitials(name) {
        if (!name) return 'U';
        const names = name.split(' ');
        if (names.length >= 2) {
            return (names[0][0] + names[1][0]).toUpperCase();
        }
        return name[0].toUpperCase();
    }

    // Public methods
    getUserProfile() {
        return this.userProfile;
    }

    isProfileComplete() {
        return this.userProfile && 
               this.userProfile.displayName && 
               this.userProfile.displayName.trim().length > 0;
    }
}

// Create and export profile manager instance
const profileManager = new ProfileManager();

// Export for use in other modules
export { ProfileManager, profileManager };
export default profileManager;
